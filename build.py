#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本
"""
import os
import subprocess
import sys

def build_exe(use_upx=False):
    """使用PyInstaller打包程序"""

    # 检查是否安装了PyInstaller
    try:
        import PyInstaller
    except ImportError:
        print("正在安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

    # 基础打包命令
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包成单个exe文件
        "--windowed",  # 不显示控制台窗口
        "--icon=hospital.ico",  # 使用图标
        "--add-data=points_exchange.db;.",  # 包含数据库文件
    ]

    # 根据是否使用UPX设置不同的名称和参数
    if use_upx:
        upx_path = r"C:\Users\<USER>\Desktop\upx-4.2.4-win64"
        if os.path.exists(upx_path):
            cmd.extend([
                f"--upx-dir={upx_path}",
                "--name=积分兑换系统_压缩版"
            ])
            print("使用UPX压缩打包...")
        else:
            print(f"UPX路径不存在: {upx_path}")
            print("将使用普通打包...")
            use_upx = False

    if not use_upx:
        cmd.append("--name=积分兑换系统")

    cmd.append("main.py")

    print("开始打包...")
    print(f"执行命令: {' '.join(cmd)}")

    try:
        subprocess.check_call(cmd)
        print("打包完成！")
        if use_upx:
            print("可执行文件位置: dist/积分兑换系统_压缩版.exe")
        else:
            print("可执行文件位置: dist/积分兑换系统.exe")
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False

    return True

def build_both():
    """打包两个版本：普通版和压缩版"""
    print("=== 开始打包普通版本 ===")
    success1 = build_exe(use_upx=False)

    print("\n=== 开始打包UPX压缩版本 ===")
    success2 = build_exe(use_upx=True)

    if success1 and success2:
        print("\n=== 打包完成 ===")
        print("普通版本: dist/积分兑换系统.exe")
        print("压缩版本: dist/积分兑换系统_压缩版.exe")

        # 显示文件大小对比
        try:
            import os
            normal_size = os.path.getsize("dist/积分兑换系统.exe")
            compressed_size = os.path.getsize("dist/积分兑换系统_压缩版.exe")
            savings = normal_size - compressed_size
            savings_percent = (savings / normal_size) * 100

            print(f"\n文件大小对比:")
            print(f"普通版本: {normal_size:,} 字节 ({normal_size/1024/1024:.2f} MB)")
            print(f"压缩版本: {compressed_size:,} 字节 ({compressed_size/1024/1024:.2f} MB)")
            print(f"节省空间: {savings:,} 字节 ({savings/1024/1024:.2f} MB)")
            print(f"压缩率: {savings_percent:.2f}%")
        except:
            pass

    return success1 and success2

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "--upx":
            build_exe(use_upx=True)
        elif sys.argv[1] == "--both":
            build_both()
        elif sys.argv[1] == "--normal":
            build_exe(use_upx=False)
        else:
            print("用法:")
            print("  python build.py          # 打包普通版本")
            print("  python build.py --normal # 打包普通版本")
            print("  python build.py --upx    # 打包UPX压缩版本")
            print("  python build.py --both   # 打包两个版本")
    else:
        # 默认打包两个版本
        build_both()
