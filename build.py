#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本
"""
import os
import subprocess
import sys

def build_exe():
    """使用PyInstaller打包程序"""
    
    # 检查是否安装了PyInstaller
    try:
        import PyInstaller
    except ImportError:
        print("正在安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # 打包命令
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包成单个exe文件
        "--windowed",  # 不显示控制台窗口
        "--icon=hospital.ico",  # 使用图标
        "--add-data=points_exchange.db;.",  # 包含数据库文件
        "--name=积分兑换系统",  # 设置程序名称
        "main.py"
    ]
    
    print("开始打包...")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        subprocess.check_call(cmd)
        print("打包完成！")
        print("可执行文件位置: dist/积分兑换系统.exe")
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    build_exe()
