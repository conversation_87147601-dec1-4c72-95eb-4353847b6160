# 伊州区东河街道卫生服务中心积分兑换系统

## 🎯 项目简介
这是一个为伊州区东河街道卫生服务中心开发的积分兑换管理系统，用于管理居民健康积分的获取和兑换。

## ✨ 主要功能
- 👥 **居民管理**: 居民信息的增删改查
- 📊 **积分规则**: 设置和管理积分获取规则
- 📝 **积分记录**: 记录居民积分获得情况
- 🎁 **奖品管理**: 管理可兑换奖品和库存
- 🔄 **兑换记录**: 查看和管理兑换历史
- 📋 **数据导入导出**: 支持Excel格式
- 📜 **操作日志**: 记录所有关键操作

## 🚀 快速开始

### 开发环境运行
```bash
# 安装依赖
pip install -r requirements.txt

# 运行程序
python main.py
```

### 打包部署
```bash
# 使用提供的打包脚本
python build.py

# 或手动打包
pyinstaller --onefile --windowed --icon=hospital.ico --add-data="points_exchange.db;." --name="积分兑换系统" main.py
```

## 📦 部署说明
- **可执行文件**: `dist/积分兑换系统.exe`
- **文件大小**: 62.4 MB
- **系统要求**: Windows 10/11 (64位)
- **登录账号**: admin / ddjd1234

## 🛠️ 技术栈
- **开发语言**: Python 3.10
- **GUI框架**: PyQt5
- **数据库**: SQLite
- **Excel处理**: openpyxl
- **打包工具**: PyInstaller

## 📁 项目结构
```
├── main.py                 # 主程序文件
├── hospital.ico           # 程序图标
├── points_exchange.db     # SQLite数据库
├── requirements.txt       # 依赖包列表
├── build.py              # 打包脚本
├── dist/                 # 打包输出目录
│   └── 积分兑换系统.exe   # 可执行文件
└── README.md             # 项目说明
```

## 🔧 最近修复
- ✅ 修复了打包后数据库路径不一致的问题
- ✅ 统一了所有数据库连接的路径管理
- ✅ 优化了程序启动流程
- ✅ 添加了详细的错误处理

## 📞 支持
如有问题请联系技术支持，并提供：
- 错误信息截图
- 操作系统版本
- 具体操作步骤

---
*开发时间: 2024年*  
*版本: v1.0*
