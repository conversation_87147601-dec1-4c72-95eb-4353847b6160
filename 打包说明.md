# 积分兑换系统打包说明

## 打包完成信息

✅ **打包成功！**

### 生成的文件
- **文件位置**: `dist/积分兑换系统.exe`
- **文件大小**: 62.4 MB
- **文件类型**: Windows可执行文件 (.exe)

### 打包参数
```bash
pyinstaller --onefile --windowed --icon=hospital.ico --add-data="points_exchange.db;." --name="积分兑换系统" main.py
```

### 参数说明
- `--onefile`: 打包成单个exe文件，便于分发
- `--windowed`: 不显示控制台窗口，提供更好的用户体验
- `--icon=hospital.ico`: 使用医院图标作为程序图标
- `--add-data="points_exchange.db;."`: 将数据库文件包含在exe中
- `--name="积分兑换系统"`: 设置程序名称

## 使用说明

### 1. 分发程序
- 将 `dist/积分兑换系统.exe` 文件复制到目标电脑
- 无需安装Python环境
- 双击即可运行

### 2. 登录信息
- **用户名**: admin
- **密码**: ddjd1234

### 3. 数据库
- 程序会自动在exe文件所在目录创建数据库文件
- 首次运行会自动初始化数据库结构
- 包含示例数据（居民信息、积分规则等）

## 功能特点

### ✅ 已修复的问题
1. **数据库路径问题**: 统一使用绝对路径，确保在任何环境下都能正确访问数据库
2. **登录报错**: 修复了打包后登录时的数据库连接问题
3. **路径一致性**: 所有数据库操作都使用统一的路径管理

### 📋 主要功能
1. **居民管理**: 添加、编辑、删除、搜索居民信息
2. **积分规则**: 管理积分获取规则和条件
3. **积分记录**: 记录居民积分获得情况
4. **奖品管理**: 管理可兑换的奖品和库存
5. **兑换记录**: 记录积分兑换历史
6. **数据导入导出**: 支持Excel格式的数据导入导出

## 技术信息

### 开发环境
- Python 3.10
- PyQt5 (GUI框架)
- SQLite (数据库)
- openpyxl (Excel处理)

### 打包工具
- PyInstaller 6.11.1

### 系统要求
- Windows 10/11
- 64位系统
- 至少100MB可用磁盘空间

## 注意事项

1. **首次运行**: 程序首次运行时会自动创建数据库文件
2. **数据备份**: 建议定期备份数据库文件 `points_exchange.db`
3. **权限要求**: 程序需要在其所在目录有读写权限
4. **防火墙**: 如果被防火墙拦截，请添加到信任列表

## 故障排除

### 如果程序无法启动
1. 检查是否有足够的磁盘空间
2. 确认程序有读写权限
3. 尝试以管理员身份运行

### 如果登录失败
1. 确认用户名密码正确（admin/ddjd1234）
2. 检查数据库文件是否正常创建

### 如果数据丢失
1. 查看程序目录下是否有 `points_exchange.db` 文件
2. 如果有备份，可以替换该文件

## 联系支持
如有问题，请联系技术支持并提供：
- 错误信息截图
- 操作系统版本
- 程序版本信息
