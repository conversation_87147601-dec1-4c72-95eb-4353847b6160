import sys
import sqlite3
import hashlib
import base64
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
    QPushButton, QLabel, QListWidget, QStackedWidget, QTableWidget,
    QTableWidgetItem, QHeaderView, QLineEdit, QComboBox, QAbstractItemView, QSpacerItem, QSizePolicy, QDialog, QFormLayout, QMessageBox, QFileDialog
)
from PyQt5.QtGui import QPixmap, QFont, QColor, QPalette
from PyQt5.QtCore import Qt
import re
import os
import datetime
import random
import subprocess
try:
    import openpyxl
except ImportError:
    openpyxl = None

# 数据库路径将在程序启动时动态设置
DB_PATH = None
# 数据库密码（与登录密码相同）
DB_PASSWORD = None

def set_db_path():
    """设置数据库路径"""
    global DB_PATH
    if DB_PATH is None:
        DB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'points_exchange.db')
    return DB_PATH

def get_db_path():
    """获取数据库路径，如果未设置则自动设置"""
    if DB_PATH is None:
        set_db_path()
    return DB_PATH

def set_db_password(password):
    """设置数据库密码"""
    global DB_PASSWORD
    DB_PASSWORD = password

def verify_db_access():
    """验证数据库访问权限"""
    if DB_PASSWORD is None:
        return False
    # 这里可以添加更复杂的验证逻辑
    return DB_PASSWORD == 'ddjd1234'

def get_secure_connection():
    """获取安全的数据库连接"""
    if not verify_db_access():
        raise PermissionError("数据库访问权限验证失败")
    return sqlite3.connect(get_db_path())

def safe_db_operation(operation_func, *args, **kwargs):
    """安全的数据库操作包装器"""
    try:
        return operation_func(*args, **kwargs)
    except PermissionError as e:
        QMessageBox.critical(None, "数据库访问错误", f"数据库访问权限验证失败：{e}")
        return None
    except Exception as e:
        QMessageBox.critical(None, "数据库操作错误", f"数据库操作失败：{e}")
        return None

def require_db_auth(func):
    """数据库操作权限验证装饰器"""
    def wrapper(*args, **kwargs):
        if not verify_db_access():
            QMessageBox.critical(None, "权限错误", "数据库访问权限验证失败！请重新登录。")
            return None
        return func(*args, **kwargs)
    return wrapper

# 重写get_db_path函数，添加权限检查
def get_db_connection():
    """获取数据库连接（带权限验证）"""
    if not verify_db_access():
        raise PermissionError("数据库访问权限验证失败")
    return sqlite3.connect(get_db_path())

# 数据库安全配置管理
def create_db_config():
    """创建数据库配置文件"""
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.db_config')
    # 简单的配置信息加密存储
    config_data = {
        'db_name': 'points_exchange.db',
        'created_time': datetime.datetime.now().isoformat(),
        'access_hash': hashlib.sha256('ddjd1234'.encode()).hexdigest()
    }

    # 将配置编码存储
    config_str = str(config_data)
    encoded_config = base64.b64encode(config_str.encode()).decode()

    try:
        with open(config_path, 'w') as f:
            f.write(encoded_config)
    except:
        pass  # 如果无法创建配置文件，不影响主要功能

def verify_db_config():
    """验证数据库配置"""
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.db_config')
    if not os.path.exists(config_path):
        create_db_config()
        return True

    try:
        with open(config_path, 'r') as f:
            encoded_config = f.read()

        config_str = base64.b64decode(encoded_config.encode()).decode()
        # 这里可以添加更多的配置验证逻辑
        return True
    except:
        return True  # 配置文件损坏时不影响使用

def enhanced_verify_db_access():
    """增强的数据库访问验证"""
    if not verify_db_config():
        return False
    return verify_db_access()

# 通用表格页面基类（优化版）
class TablePage(QWidget):
    def __init__(self, headers, parent=None):
        super().__init__(parent)
        # 主布局：左右分割
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(16)

        # 左侧：表格区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(12)

        # 表格
        self.table = QTableWidget(0, len(headers))
        self.table.setHorizontalHeaderLabels(headers)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.table.setAlternatingRowColors(True)
        self.table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setStyleSheet("""
            QTableWidget {
                background: #fff;
                font-size: 14px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
            }
            QHeaderView::section {
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8f9fa, stop:1 #e9ecef);
                font-size: 14px;
                color: #333;
                border: 1px solid #dee2e6;
                padding: 8px;
            }
            QTableWidget::item {
                padding: 6px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background: #e3f2fd;
                color: #1976d2;
            }
        """)
        self.table.verticalHeader().setDefaultSectionSize(36)
        self.table.setMinimumHeight(400)

        # 表格底部统计信息栏
        self.stats_widget = self.create_stats_widget()

        left_layout.addWidget(self.table, 1)
        left_layout.addWidget(self.stats_widget)

        # 右侧：信息面板
        self.info_panel = self.create_info_panel()

        # 添加到主布局
        main_layout.addWidget(left_widget, 3)  # 表格区域占3/4
        main_layout.addWidget(self.info_panel, 1)  # 信息面板占1/4

        # 连接表格选择事件
        self.table.itemSelectionChanged.connect(self.on_selection_changed)

    def create_stats_widget(self):
        """创建统计信息栏"""
        stats_widget = QWidget()
        stats_widget.setFixedHeight(60)
        stats_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #f8f9fa, stop:1 #e9ecef);
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin-top: 8px;
            }
        """)

        layout = QHBoxLayout(stats_widget)
        layout.setContentsMargins(16, 8, 16, 8)

        # 统计标签
        self.total_label = QLabel("总计: 0 条")
        self.total_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #495057;")

        self.selected_label = QLabel("已选: 0 条")
        self.selected_label.setStyleSheet("font-size: 14px; color: #6c757d;")

        # 快速操作按钮
        self.quick_refresh_btn = QPushButton("🔄 刷新")
        self.quick_refresh_btn.setStyleSheet("""
            QPushButton {
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background: #34ce57;
            }
        """)

        layout.addWidget(self.total_label)
        layout.addWidget(self.selected_label)
        layout.addStretch()
        layout.addWidget(self.quick_refresh_btn)

        return stats_widget

    def create_info_panel(self):
        """创建右侧信息面板"""
        panel = QWidget()
        panel.setFixedWidth(280)
        panel.setStyleSheet("""
            QWidget {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # 标题
        title = QLabel("📊 详细信息")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #495057; margin-bottom: 8px;")
        layout.addWidget(title)

        # 详细信息显示区域
        self.detail_area = QWidget()
        detail_layout = QVBoxLayout(self.detail_area)
        detail_layout.setContentsMargins(0, 0, 0, 0)

        self.detail_label = QLabel("请选择一行数据查看详细信息")
        self.detail_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 13px;
                padding: 12px;
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                min-height: 200px;
            }
        """)
        self.detail_label.setWordWrap(True)
        self.detail_label.setAlignment(Qt.AlignmentFlag.AlignTop)

        detail_layout.addWidget(self.detail_label)
        layout.addWidget(self.detail_area)

        # 快捷操作区域
        self.create_quick_actions(layout)

        layout.addStretch()
        return panel

    def create_quick_actions(self, layout):
        """创建快捷操作区域"""
        actions_title = QLabel("⚡ 快捷操作")
        actions_title.setStyleSheet("font-size: 14px; font-weight: bold; color: #495057; margin-top: 16px;")
        layout.addWidget(actions_title)

        # 快捷按钮容器
        actions_widget = QWidget()
        actions_layout = QVBoxLayout(actions_widget)
        actions_layout.setContentsMargins(0, 8, 0, 0)
        actions_layout.setSpacing(8)

        # 快捷按钮样式
        button_style = """
            QPushButton {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                text-align: left;
                color: #495057;
            }
            QPushButton:hover {
                background: #e9ecef;
                border-color: #adb5bd;
            }
        """

        self.quick_add_btn = QPushButton("➕ 快速新增")
        self.quick_edit_btn = QPushButton("✏️ 快速编辑")
        self.quick_delete_btn = QPushButton("🗑️ 快速删除")

        for btn in [self.quick_add_btn, self.quick_edit_btn, self.quick_delete_btn]:
            btn.setStyleSheet(button_style)
            actions_layout.addWidget(btn)

        # 删除按钮特殊样式
        self.quick_delete_btn.setStyleSheet(button_style.replace("#495057", "#dc3545"))

        layout.addWidget(actions_widget)

    def on_selection_changed(self):
        """处理表格选择变化"""
        selected_items = self.table.selectedItems()
        selected_rows = len(set(item.row() for item in selected_items)) if selected_items else 0

        # 更新选择计数
        self.selected_label.setText(f"已选: {selected_rows} 条")

        # 更新详细信息
        if selected_rows == 1:
            row = selected_items[0].row()
            self.show_row_details(row)
        elif selected_rows > 1:
            self.detail_label.setText(f"已选择 {selected_rows} 行数据\n\n可以进行批量操作：\n• 批量删除\n• 批量导出")
        else:
            self.detail_label.setText("请选择一行数据查看详细信息")

    def show_row_details(self, row):
        """显示行详细信息"""
        details = []
        for col in range(self.table.columnCount()):
            header = self.table.horizontalHeaderItem(col).text()
            item = self.table.item(row, col)
            value = item.text() if item else ""
            details.append(f"<b>{header}:</b> {value}")

        detail_text = "<br>".join(details)
        self.detail_label.setText(detail_text)

    def set_data(self, data):
        """设置表格数据并更新统计"""
        self.table.setRowCount(len(data))
        for row, row_data in enumerate(data):
            for col, value in enumerate(row_data):
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.table.setItem(row, col, item)

        # 更新统计信息
        self.total_label.setText(f"总计: {len(data)} 条")
        self.selected_label.setText("已选: 0 条")

        # 自动调整列宽
        self.table.resizeColumnsToContents()

        # 清空详细信息
        self.detail_label.setText("请选择一行数据查看详细信息")

# 居民信息编辑弹窗（美化+校验）
class ResidentEditDialog(QDialog):
    def __init__(self, parent=None, data=None):
        super().__init__(parent)
        self.setWindowTitle("居民信息")
        self.setFixedSize(420, 420)
        self.setStyleSheet("QDialog {background: #fafdff; border-radius: 12px;}")
        layout = QFormLayout(self)
        font = QFont("微软雅黑", 11)
        self.setFont(font)
        # 输入框
        self.name_edit = QLineEdit(); self.name_edit.setPlaceholderText("必填")
        self.id_card_edit = QLineEdit(); self.id_card_edit.setPlaceholderText("18位身份证号")
        self.gender_combo = QComboBox(); self.gender_combo.addItems(["男", "女", "其他"])
        self.age_edit = QLineEdit(); self.age_edit.setPlaceholderText("0-120")
        self.phone_edit = QLineEdit(); self.phone_edit.setPlaceholderText("11位手机号")
        self.group_combo = QComboBox(); self.group_combo.addItems(["所有人","0-6岁儿童","孕产妇","老年人","慢性病","严重精神障碍","肺结核"])
        self.doctor_phone_edit = QLineEdit(); self.doctor_phone_edit.setPlaceholderText("可选")
        self.address_edit = QLineEdit(); self.address_edit.setPlaceholderText("可选")
        self.remark_edit = QLineEdit(); self.remark_edit.setPlaceholderText("可选")
        # 必填项高亮
        for w in [self.name_edit, self.id_card_edit, self.gender_combo, self.age_edit, self.phone_edit]:
            w.setStyleSheet("background: #fffbe6;")
        layout.setVerticalSpacing(12)
        layout.addRow("姓名*", self.name_edit)
        layout.addRow("身份证号*", self.id_card_edit)
        layout.addRow("性别*", self.gender_combo)
        layout.addRow("年龄*", self.age_edit)
        layout.addRow("电话*", self.phone_edit)
        layout.addRow("分组", self.group_combo)
        layout.addRow("家庭医生", self.doctor_phone_edit)
        layout.addRow("地址", self.address_edit)
        layout.addRow("备注", self.remark_edit)
        btn_layout = QHBoxLayout()
        self.ok_btn = QPushButton("确定")
        self.ok_btn.setStyleSheet("QPushButton {background: #409eff; color: white; border-radius: 6px; padding: 6px 18px; font-size: 15px;}")
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("QPushButton {background: #e0e0e0; border-radius: 6px; padding: 6px 18px; font-size: 15px;}")
        btn_layout.addStretch()
        btn_layout.addWidget(self.ok_btn)
        btn_layout.addWidget(self.cancel_btn)
        layout.addRow(btn_layout)
        self.ok_btn.clicked.connect(self.validate_and_accept)
        self.cancel_btn.clicked.connect(self.reject)
        self.data = data
        if data:
            self.name_edit.setText(data[1])
            self.id_card_edit.setText(data[2])
            self.gender_combo.setCurrentText(data[3])
            self.age_edit.setText(str(data[4]))
            self.phone_edit.setText(data[5])
            self.group_combo.setCurrentText(data[6])
            self.doctor_phone_edit.setText(data[7])
            self.address_edit.setText(data[8])
            self.remark_edit.setText(data[9])
            self.id_card_edit.setEnabled(False)

    def validate_and_accept(self):
        name = self.name_edit.text().strip()
        id_card = self.id_card_edit.text().strip()
        gender = self.gender_combo.currentText()
        age = self.age_edit.text().strip()
        phone = self.phone_edit.text().strip()
        # 校验
        if not name or not id_card or not gender or not age or not phone:
            QMessageBox.warning(self, "校验", "带*为必填项！")
            return
        if not re.match(r"^\d{17}[\dXx]$", id_card):
            QMessageBox.warning(self, "校验", "身份证号格式不正确！")
            return
        try:
            age_int = int(age)
            if not (0 <= age_int <= 120):
                raise ValueError
        except:
            QMessageBox.warning(self, "校验", "年龄应为0-120的整数！")
            return
        if not re.match(r"^1\d{10}$", phone):
            QMessageBox.warning(self, "校验", "手机号应为11位数字！")
            return
        # 新增时校验身份证唯一
        if self.id_card_edit.isEnabled():
            try:
                conn = get_secure_connection()
            except PermissionError:
                QMessageBox.critical(self, "权限错误", "数据库访问权限验证失败！")
                return
            cur = conn.cursor()
            cur.execute("SELECT COUNT(*) FROM residents WHERE id_card=?", (id_card,))
            if cur.fetchone()[0] > 0:
                QMessageBox.warning(self, "校验", "身份证号已存在！")
                conn.close()
                return
            conn.close()
        self.accept()

    def get_data(self):
        return {
            'name': self.name_edit.text().strip(),
            'id_card': self.id_card_edit.text().strip(),
            'gender': self.gender_combo.currentText(),
            'age': self.age_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'group_type': self.group_combo.currentText(),
            'doctor_phone': self.doctor_phone_edit.text().strip(),
            'address': self.address_edit.text().strip(),
            'remark': self.remark_edit.text().strip(),
        }

# 居民信息页面
class ResidentsPage(TablePage):
    def __init__(self, parent=None):
        headers = ["ID", "姓名", "身份证号", "性别", "年龄", "电话", "分组", "家庭医生", "地址", "备注"]
        super().__init__(headers, parent)
        # 搜索栏和按钮
        top_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("姓名/身份证号")
        self.search_edit.setFixedWidth(220)
        self.search_btn = QPushButton("搜索")
        self.add_btn = QPushButton("新增")
        self.edit_btn = QPushButton("编辑")
        self.del_btn = QPushButton("删除")
        self.import_btn = QPushButton("导入Excel")
        self.export_btn = QPushButton("导出Excel")
        for btn in [self.search_btn, self.add_btn, self.edit_btn, self.del_btn, self.import_btn, self.export_btn]:
            btn.setStyleSheet("QPushButton {background: #409eff; color: white; border-radius: 6px; padding: 4px 14px; font-size: 14px;} QPushButton:hover {background: #66b1ff;}")
        self.del_btn.setStyleSheet("QPushButton {background: #f56c6c; color: white; border-radius: 6px; padding: 4px 14px; font-size: 14px;} QPushButton:hover {background: #ff8787;}")
        top_layout.addWidget(self.search_edit)
        top_layout.addWidget(self.search_btn)
        top_layout.addStretch()
        top_layout.addWidget(self.import_btn)
        top_layout.addWidget(self.export_btn)
        top_layout.addWidget(self.add_btn)
        top_layout.addWidget(self.edit_btn)
        top_layout.addWidget(self.del_btn)
        self.layout().insertLayout(0, top_layout)
        self.search_btn.clicked.connect(self.do_search)
        self.add_btn.clicked.connect(self.do_add)
        self.edit_btn.clicked.connect(self.do_edit)
        self.del_btn.clicked.connect(self.do_delete)
        self.import_btn.clicked.connect(self.do_import)
        self.export_btn.clicked.connect(self.do_export)

        # 连接快捷操作按钮
        self.quick_refresh_btn.clicked.connect(self.refresh)
        self.quick_add_btn.clicked.connect(self.do_add)
        self.quick_edit_btn.clicked.connect(self.do_edit)
        self.quick_delete_btn.clicked.connect(self.do_delete)

        self.refresh()

    def show_row_details(self, row):
        """显示居民详细信息"""
        if row >= self.table.rowCount():
            return

        # 获取居民基本信息
        resident_id = self.table.item(row, 0).text() if self.table.item(row, 0) else ""
        name = self.table.item(row, 1).text() if self.table.item(row, 1) else ""
        id_card = self.table.item(row, 2).text() if self.table.item(row, 2) else ""
        gender = self.table.item(row, 3).text() if self.table.item(row, 3) else ""
        age = self.table.item(row, 4).text() if self.table.item(row, 4) else ""
        phone = self.table.item(row, 5).text() if self.table.item(row, 5) else ""
        group_type = self.table.item(row, 6).text() if self.table.item(row, 6) else ""

        # 查询积分统计
        try:
            conn = sqlite3.connect(get_db_path())
            cur = conn.cursor()

            # 查询总积分
            cur.execute("""
                SELECT COALESCE(SUM(points), 0) as total_points
                FROM point_records
                WHERE resident_id = ?
            """, (id_card,))
            total_points = cur.fetchone()[0]

            # 查询兑换次数
            cur.execute("""
                SELECT COUNT(*) as exchange_count
                FROM exchange_records
                WHERE resident_id = ?
            """, (id_card,))
            exchange_count = cur.fetchone()[0]

            # 查询最近积分记录
            cur.execute("""
                SELECT record_time, points, pr.name
                FROM point_records pr
                LEFT JOIN point_rules r ON pr.rule_id = r.id
                WHERE pr.resident_id = ?
                ORDER BY pr.record_time DESC
                LIMIT 3
            """, (id_card,))
            recent_records = cur.fetchall()

            conn.close()
        except:
            total_points = 0
            exchange_count = 0
            recent_records = []

        # 构建详细信息HTML
        details_html = f"""
        <div style="font-family: '微软雅黑'; line-height: 1.6;">
            <h3 style="color: #2c3e50; margin-bottom: 12px;">👤 {name}</h3>

            <div style="background: #ecf0f1; padding: 8px; border-radius: 4px; margin-bottom: 12px;">
                <strong>🆔 ID:</strong> {resident_id}<br>
                <strong>📱 电话:</strong> {phone}<br>
                <strong>👥 分组:</strong> {group_type}<br>
                <strong>🎂 年龄:</strong> {age}岁 ({gender})
            </div>

            <div style="background: #e8f5e8; padding: 8px; border-radius: 4px; margin-bottom: 12px;">
                <strong>💰 总积分:</strong> <span style="color: #27ae60; font-size: 16px; font-weight: bold;">{total_points}</span><br>
                <strong>🎁 兑换次数:</strong> {exchange_count} 次
            </div>
        """

        if recent_records:
            details_html += """
            <div style="background: #fff3cd; padding: 8px; border-radius: 4px;">
                <strong>📈 最近积分记录:</strong><br>
            """
            for record in recent_records:
                record_time, points, rule_name = record
                details_html += f"• {record_time[:10]} +{points}分 ({rule_name or '未知'})<br>"
            details_html += "</div>"

        details_html += "</div>"

        self.detail_label.setText(details_html)

    def refresh(self, keyword=None):
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        if keyword:
            cur.execute("SELECT id, name, id_card, gender, age, phone, group_type, doctor_phone, address, remark FROM residents WHERE name LIKE ? OR id_card LIKE ?", (f"%{keyword}%", f"%{keyword}%"))
        else:
            cur.execute("SELECT id, name, id_card, gender, age, phone, group_type, doctor_phone, address, remark FROM residents")
        data = cur.fetchall()
        conn.close()
        self.set_data(data)

    def do_search(self):
        keyword = self.search_edit.text().strip()
        self.refresh(keyword)

    def do_add(self):
        dlg = ResidentEditDialog(self)
        if dlg.exec_() == QDialog.Accepted:
            d = dlg.get_data()
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("INSERT INTO residents (name, id_card, gender, age, phone, group_type, doctor_phone, address, remark) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    (d['name'], d['id_card'], d['gender'], d['age'], d['phone'], d['group_type'], d['doctor_phone'], d['address'], d['remark']))
                conn.commit()
                conn.close()
                QMessageBox.information(self, "提示", "新增成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"新增失败：{e}")

    def do_edit(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要编辑的居民！")
            return
        row = selected[0].row()
        data = [self.table.item(row, i).text() for i in range(self.table.columnCount())]
        dlg = ResidentEditDialog(self, data)
        if dlg.exec_() == QDialog.Accepted:
            d = dlg.get_data()
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("UPDATE residents SET name=?, gender=?, age=?, phone=?, group_type=?, doctor_phone=?, address=?, remark=? WHERE id_card=?",
                    (d['name'], d['gender'], d['age'], d['phone'], d['group_type'], d['doctor_phone'], d['address'], d['remark'], d['id_card']))
                conn.commit()
                conn.close()
                QMessageBox.information(self, "提示", "修改成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"修改失败：{e}")

    def do_delete(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要删除的居民！")
            return
        rows = set(item.row() for item in selected)
        id_cards = [self.table.item(row, 2).text() for row in rows]
        # 检查是否有关联积分/兑换记录
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        for idc in id_cards:
            cur.execute("SELECT COUNT(*) FROM point_records WHERE resident_id=?", (idc,))
            if cur.fetchone()[0] > 0:
                QMessageBox.warning(self, "错误", f"居民{idc}有关联积分记录，禁止删除！")
                conn.close()
                return
            cur.execute("SELECT COUNT(*) FROM exchange_records WHERE resident_id=?", (idc,))
            if cur.fetchone()[0] > 0:
                QMessageBox.warning(self, "错误", f"居民{idc}有关联兑换记录，禁止删除！")
                conn.close()
                return
        if QMessageBox.question(self, "确认", f"确定要删除选中的{len(id_cards)}位居民吗？", QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No) == QMessageBox.StandardButton.Yes:
            try:
                cur.executemany("DELETE FROM residents WHERE id_card=?", [(idc,) for idc in id_cards])
                conn.commit()
                log_operation('删除居民', f"身份证:{','.join(id_cards)}")
                conn.close()
                QMessageBox.information(self, "提示", "删除成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除失败：{e}")

    def do_export(self):
        if openpyxl is None:
            QMessageBox.warning(self, "提示", "请先安装openpyxl库：pip install openpyxl")
            return
        path, _ = QFileDialog.getSaveFileName(self, "导出Excel", "居民信息.xlsx", "Excel Files (*.xlsx)")
        if not path:
            return
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.append([self.table.horizontalHeaderItem(i).text() for i in range(self.table.columnCount())])
        for row in range(self.table.rowCount()):
            ws.append([self.table.item(row, col).text() if self.table.item(row, col) else '' for col in range(self.table.columnCount())])
        try:
            wb.save(path)
            QMessageBox.information(self, "提示", f"导出成功：{os.path.basename(path)}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"导出失败：{e}")

    def do_import(self):
        if openpyxl is None:
            QMessageBox.warning(self, "提示", "请先安装openpyxl库：pip install openpyxl")
            return
        path, _ = QFileDialog.getOpenFileName(self, "导入Excel", "", "Excel Files (*.xlsx)")
        if not path:
            return
        wb = openpyxl.load_workbook(path)
        ws = wb.active
        rows = list(ws.iter_rows(min_row=2, values_only=True))
        success, fail = 0, 0
        for row in rows:
            try:
                d = {
                    'name': row[1],
                    'id_card': row[2],
                    'gender': row[3],
                    'age': row[4],
                    'phone': row[5],
                    'group_type': row[6],
                    'doctor_phone': row[7],
                    'address': row[8],
                    'remark': row[9],
                }
                # 校验
                if not d['name'] or not d['id_card'] or not d['gender'] or not d['age'] or not d['phone']:
                    fail += 1; continue
                if not re.match(r"^\d{17}[\dXx]$", d['id_card']):
                    fail += 1; continue
                try:
                    age_int = int(d['age'])
                    if not (0 <= age_int <= 120):
                        fail += 1; continue
                except:
                    fail += 1; continue
                if not re.match(r"^1\d{10}$", d['phone']):
                    fail += 1; continue
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("SELECT COUNT(*) FROM residents WHERE id_card=?", (d['id_card'],))
                if cur.fetchone()[0] > 0:
                    conn.close(); fail += 1; continue
                cur.execute("INSERT INTO residents (name, id_card, gender, age, phone, group_type, doctor_phone, address, remark) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    (d['name'], d['id_card'], d['gender'], d['age'], d['phone'], d['group_type'], d['doctor_phone'], d['address'], d['remark']))
                conn.commit(); conn.close(); success += 1
            except Exception:
                fail += 1
        self.refresh()
        QMessageBox.information(self, "导入结果", f"成功导入{success}条，失败{fail}条。")

# 积分项编辑弹窗
class PointRuleEditDialog(QDialog):
    def __init__(self, parent=None, data=None):
        super().__init__(parent)
        self.setWindowTitle("积分规则")
        self.setFixedSize(480, 420)
        self.setStyleSheet("QDialog {background: #fafdff; border-radius: 12px;}")
        layout = QFormLayout(self)
        font = QFont("微软雅黑", 11)
        self.setFont(font)
        self.rule_code_edit = QLineEdit(); self.rule_code_edit.setPlaceholderText("必填，唯一编码")
        self.name_edit = QLineEdit(); self.name_edit.setPlaceholderText("必填")
        self.category_edit = QLineEdit(); self.category_edit.setPlaceholderText("必填")
        self.points_edit = QLineEdit(); self.points_edit.setPlaceholderText("正整数")
        self.description_edit = QLineEdit(); self.description_edit.setPlaceholderText("可选")
        self.valid_from_edit = QLineEdit(); self.valid_from_edit.setPlaceholderText("YYYY-MM-DD")
        self.valid_to_edit = QLineEdit(); self.valid_to_edit.setPlaceholderText("YYYY-MM-DD，可选")
        self.max_apply_times_edit = QLineEdit(); self.max_apply_times_edit.setPlaceholderText("0或正整数")
        self.apply_interval_combo = QComboBox(); self.apply_interval_combo.addItems(["日度","月度","季度","年度"])
        for w in [self.rule_code_edit, self.name_edit, self.category_edit, self.points_edit]:
            w.setStyleSheet("background: #fffbe6;")
        layout.setVerticalSpacing(12)
        layout.addRow("规则编码*", self.rule_code_edit)
        layout.addRow("名称*", self.name_edit)
        layout.addRow("类别*", self.category_edit)
        layout.addRow("积分*", self.points_edit)
        layout.addRow("描述", self.description_edit)
        layout.addRow("有效期起*", self.valid_from_edit)
        layout.addRow("有效期止", self.valid_to_edit)
        layout.addRow("最大次数", self.max_apply_times_edit)
        layout.addRow("周期*", self.apply_interval_combo)
        btn_layout = QHBoxLayout()
        self.ok_btn = QPushButton("确定")
        self.ok_btn.setStyleSheet("QPushButton {background: #409eff; color: white; border-radius: 6px; padding: 6px 18px; font-size: 15px;}")
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("QPushButton {background: #e0e0e0; border-radius: 6px; padding: 6px 18px; font-size: 15px;}")
        btn_layout.addStretch()
        btn_layout.addWidget(self.ok_btn)
        btn_layout.addWidget(self.cancel_btn)
        layout.addRow(btn_layout)
        self.ok_btn.clicked.connect(self.validate_and_accept)
        self.cancel_btn.clicked.connect(self.reject)
        if data:
            self.rule_code_edit.setText(data[1])
            self.name_edit.setText(data[2])
            self.category_edit.setText(data[3])
            self.points_edit.setText(str(data[4]))
            self.description_edit.setText(data[5])
            self.valid_from_edit.setText(data[6])
            self.valid_to_edit.setText(data[7])
            self.max_apply_times_edit.setText(str(data[8]))
            self.apply_interval_combo.setCurrentText(data[9])
            self.rule_code_edit.setEnabled(False)
    def validate_and_accept(self):
        rule_code = self.rule_code_edit.text().strip()
        name = self.name_edit.text().strip()
        category = self.category_edit.text().strip()
        points = self.points_edit.text().strip()
        valid_from = self.valid_from_edit.text().strip()
        apply_interval = self.apply_interval_combo.currentText()
        if not rule_code or not name or not category or not points or not valid_from or not apply_interval:
            QMessageBox.warning(self, "校验", "带*为必填项！")
            return
        try:
            points_int = int(points)
            if points_int <= 0:
                raise ValueError
        except:
            QMessageBox.warning(self, "校验", "积分应为正整数！")
            return
        max_apply_times = self.max_apply_times_edit.text().strip() or '0'
        try:
            max_apply_times_int = int(max_apply_times)
            if max_apply_times_int < 0:
                raise ValueError
        except:
            QMessageBox.warning(self, "校验", "最大次数应为0或正整数！")
            return
        # 新增时校验编码唯一
        if self.rule_code_edit.isEnabled():
            conn = sqlite3.connect(get_db_path())
            cur = conn.cursor()
            cur.execute("SELECT COUNT(*) FROM point_rules WHERE rule_code=?", (rule_code,))
            if cur.fetchone()[0] > 0:
                QMessageBox.warning(self, "校验", "规则编码已存在！")
                conn.close()
                return
            conn.close()
        self.accept()
    def get_data(self):
        return {
            'rule_code': self.rule_code_edit.text().strip(),
            'name': self.name_edit.text().strip(),
            'category': self.category_edit.text().strip(),
            'points': self.points_edit.text().strip(),
            'description': self.description_edit.text().strip(),
            'valid_from': self.valid_from_edit.text().strip(),
            'valid_to': self.valid_to_edit.text().strip(),
            'max_apply_times': self.max_apply_times_edit.text().strip() or '0',
            'apply_interval': self.apply_interval_combo.currentText(),
        }

# 积分项页面
class PointRulesPage(TablePage):
    def __init__(self, parent=None):
        headers = ["规则编码", "名称", "类别", "积分", "描述", "有效期起", "有效期止", "最大次数", "周期"]
        super().__init__(headers, parent)
        # 搜索栏和按钮
        top_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("规则名称/编码")
        self.search_edit.setFixedWidth(220)
        self.search_btn = QPushButton("搜索")
        self.add_btn = QPushButton("新增")
        self.edit_btn = QPushButton("编辑")
        self.del_btn = QPushButton("删除")
        self.import_btn = QPushButton("导入Excel")
        self.export_btn = QPushButton("导出Excel")
        for btn in [self.search_btn, self.add_btn, self.edit_btn, self.del_btn, self.import_btn, self.export_btn]:
            btn.setStyleSheet("QPushButton {background: #409eff; color: white; border-radius: 6px; padding: 4px 14px; font-size: 14px;} QPushButton:hover {background: #66b1ff;}")
        self.del_btn.setStyleSheet("QPushButton {background: #f56c6c; color: white; border-radius: 6px; padding: 4px 14px; font-size: 14px;} QPushButton:hover {background: #ff8787;}")
        top_layout.addWidget(self.search_edit)
        top_layout.addWidget(self.search_btn)
        top_layout.addStretch()
        top_layout.addWidget(self.import_btn)
        top_layout.addWidget(self.export_btn)
        top_layout.addWidget(self.add_btn)
        top_layout.addWidget(self.edit_btn)
        top_layout.addWidget(self.del_btn)
        self.layout().insertLayout(0, top_layout)
        self.search_btn.clicked.connect(self.do_search)
        self.add_btn.clicked.connect(self.do_add)
        self.edit_btn.clicked.connect(self.do_edit)
        self.del_btn.clicked.connect(self.do_delete)
        self.import_btn.clicked.connect(self.do_import)
        self.export_btn.clicked.connect(self.do_export)

        # 连接快捷操作按钮
        self.quick_refresh_btn.clicked.connect(self.refresh)
        self.quick_add_btn.clicked.connect(self.do_add)
        self.quick_edit_btn.clicked.connect(self.do_edit)
        self.quick_delete_btn.clicked.connect(self.do_delete)

        self.refresh()
    def refresh(self, keyword=None):
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        if keyword:
            cur.execute("SELECT rule_code, name, category, points, description, valid_from, valid_to, max_apply_times, apply_interval FROM point_rules WHERE name LIKE ? OR rule_code LIKE ?", (f"%{keyword}%", f"%{keyword}%"))
        else:
            cur.execute("SELECT rule_code, name, category, points, description, valid_from, valid_to, max_apply_times, apply_interval FROM point_rules")
        data = cur.fetchall()
        conn.close()
        self.set_data(data)
    def do_search(self):
        keyword = self.search_edit.text().strip()
        self.refresh(keyword)
    def do_add(self):
        dlg = PointRuleEditDialog(self)
        if dlg.exec_() == QDialog.Accepted:
            d = dlg.get_data()
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("INSERT INTO point_rules (rule_code, name, category, points, description, valid_from, valid_to, max_apply_times, apply_interval) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    (d['rule_code'], d['name'], d['category'], d['points'], d['description'], d['valid_from'], d['valid_to'] or None, d['max_apply_times'], d['apply_interval']))
                conn.commit()
                conn.close()
                QMessageBox.information(self, "提示", "新增成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"新增失败：{e}")
    def do_edit(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要编辑的规则！")
            return
        row = selected[0].row()
        data = [self.table.item(row, i).text() for i in range(self.table.columnCount())]
        dlg = PointRuleEditDialog(self, data)
        if dlg.exec_() == QDialog.Accepted:
            d = dlg.get_data()
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("UPDATE point_rules SET name=?, category=?, points=?, description=?, valid_from=?, valid_to=?, max_apply_times=?, apply_interval=? WHERE rule_code=?",
                    (d['name'], d['category'], d['points'], d['description'], d['valid_from'], d['valid_to'] or None, d['max_apply_times'], d['apply_interval'], d['rule_code']))
                conn.commit()
                conn.close()
                QMessageBox.information(self, "提示", "修改成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"修改失败：{e}")
    def do_delete(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要删除的规则！")
            return
        rows = set(item.row() for item in selected)
        rule_codes = [self.table.item(row, 0).text() for row in rows]
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        for code in rule_codes:
            cur.execute("SELECT COUNT(*) FROM point_records pr LEFT JOIN point_rules r ON pr.rule_id=r.id WHERE r.rule_code=?", (code,))
            if cur.fetchone()[0] > 0:
                QMessageBox.warning(self, "错误", f"规则{code}已被积分记录引用，禁止删除！")
                conn.close()
                return
        if QMessageBox.question(self, "确认", f"确定要删除选中的{len(rule_codes)}条规则吗？", QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No) == QMessageBox.StandardButton.Yes:
            try:
                cur.executemany("DELETE FROM point_rules WHERE rule_code=?", [(c,) for c in rule_codes])
                conn.commit()
                log_operation('删除积分规则', f"规则编码:{','.join(rule_codes)}")
                conn.close()
                QMessageBox.information(self, "提示", "删除成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除失败：{e}")
    def do_export(self):
        if openpyxl is None:
            QMessageBox.warning(self, "提示", "请先安装openpyxl库：pip install openpyxl")
            return
        path, _ = QFileDialog.getSaveFileName(self, "导出Excel", "积分规则.xlsx", "Excel Files (*.xlsx)")
        if not path:
            return
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.append([self.table.horizontalHeaderItem(i).text() for i in range(self.table.columnCount())])
        for row in range(self.table.rowCount()):
            ws.append([self.table.item(row, col).text() if self.table.item(row, col) else '' for col in range(self.table.columnCount())])
        try:
            wb.save(path)
            QMessageBox.information(self, "提示", f"导出成功：{os.path.basename(path)}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"导出失败：{e}")
    def do_import(self):
        if openpyxl is None:
            QMessageBox.warning(self, "提示", "请先安装openpyxl库：pip install openpyxl")
            return
        path, _ = QFileDialog.getOpenFileName(self, "导入Excel", "", "Excel Files (*.xlsx)")
        if not path:
            return
        wb = openpyxl.load_workbook(path)
        ws = wb.active
        rows = list(ws.iter_rows(min_row=2, values_only=True))
        success, fail = 0, 0
        for row in rows:
            try:
                d = {
                    'rule_code': row[0],
                    'name': row[1],
                    'category': row[2],
                    'points': row[3],
                    'description': row[4],
                    'valid_from': row[5],
                    'valid_to': row[6],
                    'max_apply_times': row[7] if row[7] is not None else '0',
                    'apply_interval': row[8],
                }
                if not d['rule_code'] or not d['name'] or not d['category'] or not d['points'] or not d['valid_from'] or not d['apply_interval']:
                    fail += 1; continue
                try:
                    points_int = int(d['points'])
                    if points_int <= 0:
                        fail += 1; continue
                except:
                    fail += 1; continue
                try:
                    max_apply_times_int = int(d['max_apply_times'])
                    if max_apply_times_int < 0:
                        fail += 1; continue
                except:
                    fail += 1; continue
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("SELECT COUNT(*) FROM point_rules WHERE rule_code=?", (d['rule_code'],))
                if cur.fetchone()[0] > 0:
                    conn.close(); fail += 1; continue
                cur.execute("INSERT INTO point_rules (rule_code, name, category, points, description, valid_from, valid_to, max_apply_times, apply_interval) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    (d['rule_code'], d['name'], d['category'], d['points'], d['description'], d['valid_from'], d['valid_to'] or None, d['max_apply_times'], d['apply_interval']))
                conn.commit(); conn.close(); success += 1
            except Exception:
                fail += 1
        self.refresh()
        QMessageBox.information(self, "导入结果", f"成功导入{success}条，失败{fail}条。")

# 积分记录编辑弹窗
class PointRecordEditDialog(QDialog):
    def __init__(self, parent=None, data=None):
        super().__init__(parent)
        self.setWindowTitle("积分记录")
        self.setFixedSize(480, 400)
        self.setStyleSheet("QDialog {background: #fafdff; border-radius: 12px;}")
        layout = QFormLayout(self)
        font = QFont("微软雅黑", 12)
        self.setFont(font)
        # 自动生成记录编号
        if data:
            record_no = data[1]
        else:
            now = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
            rand = random.randint(100, 999)
            record_no = f'PR{now}{rand}'
        self.record_no_edit = QLineEdit(record_no)
        self.record_no_edit.setReadOnly(True)
        self.record_no_edit.setStyleSheet("background: #f0f0f0; color: #888;")
        self.resident_id_edit = QLineEdit(); self.resident_id_edit.setPlaceholderText("居民身份证号")
        # 二级下拉：类别、名称
        self.category_combo = QComboBox(); self.category_combo.setPlaceholderText("请选择类别")
        self.name_combo = QComboBox(); self.name_combo.setPlaceholderText("请选择名称")
        self.rule_id_edit = QLineEdit(); self.rule_id_edit.setVisible(False)
        self.points_edit = QLineEdit(); self.points_edit.setPlaceholderText("正整数")
        # 记录时间默认当前日期
        if data:
            record_time = data[5]
        else:
            record_time = datetime.datetime.now().strftime('%Y-%m-%d')
        self.record_time_edit = QLineEdit(record_time)
        self.record_time_edit.setPlaceholderText("YYYY-MM-DD，可选")
        self.operator_id_edit = QLineEdit(); self.operator_id_edit.setPlaceholderText("操作人ID")
        self.status_combo = QComboBox(); self.status_combo.addItems(["有效","已过期","已冻结"])
        for w in [self.record_no_edit, self.resident_id_edit, self.category_combo, self.name_combo, self.points_edit, self.operator_id_edit]:
            w.setStyleSheet("background: #fffbe6;")
        layout.setVerticalSpacing(14)
        layout.addRow("记录编号*", self.record_no_edit)
        layout.addRow("居民身份证*", self.resident_id_edit)
        layout.addRow("类别*", self.category_combo)
        layout.addRow("名称*", self.name_combo)
        layout.addRow("积分*", self.points_edit)
        layout.addRow("记录时间", self.record_time_edit)
        layout.addRow("操作人*", self.operator_id_edit)
        layout.addRow("状态", self.status_combo)
        btn_layout = QHBoxLayout()
        self.ok_btn = QPushButton("确定")
        self.ok_btn.setStyleSheet("QPushButton {background: #409eff; color: white; border-radius: 6px; padding: 8px 24px; font-size: 16px;}")
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("QPushButton {background: #e0e0e0; border-radius: 6px; padding: 8px 24px; font-size: 16px;}")
        btn_layout.addStretch()
        btn_layout.addWidget(self.ok_btn)
        btn_layout.addSpacing(40)
        btn_layout.addWidget(self.cancel_btn)
        btn_layout.addStretch()
        layout.addRow(btn_layout)
        self.ok_btn.clicked.connect(self.validate_and_accept)
        self.cancel_btn.clicked.connect(self.reject)
        # 加载积分项数据
        self.rule_map = {}  # {类别: [{id, name, points}]}
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute("SELECT id, category, name, points FROM point_rules")
        for rid, cat, name, pts in cur.fetchall():
            if cat not in self.rule_map:
                self.rule_map[cat] = []
            self.rule_map[cat].append({'id': rid, 'name': name, 'points': pts})
        conn.close()
        self.category_combo.addItems(self.rule_map.keys())
        self.category_combo.currentTextChanged.connect(self.on_category_changed)
        self.name_combo.currentTextChanged.connect(self.on_name_changed)
        # 编辑时回显
        if data:
            self.resident_id_edit.setText(data[2])
            # 反查类别和名称
            rule_id = data[3]
            found = False
            for cat, rules in self.rule_map.items():
                for rule in rules:
                    if str(rule['id']) == str(rule_id):
                        self.category_combo.setCurrentText(cat)
                        self.on_category_changed(cat)
                        self.name_combo.setCurrentText(rule['name'])
                        found = True
                        break
                if found:
                    break
            self.points_edit.setText(str(data[4]))
            self.record_time_edit.setText(data[5])
            self.operator_id_edit.setText(data[6])
            self.status_combo.setCurrentText(data[7])
        else:
            # 默认选第一个类别
            if self.category_combo.count() > 0:
                self.category_combo.setCurrentIndex(0)
                self.on_category_changed(self.category_combo.currentText())
        self.name_combo.setMinimumWidth(180)
        self.category_combo.setMinimumWidth(120)
    def on_category_changed(self, cat):
        self.name_combo.clear()
        if cat in self.rule_map:
            self.name_combo.addItems([r['name'] for r in self.rule_map[cat]])
            self.on_name_changed(self.name_combo.currentText())
    def on_name_changed(self, name):
        cat = self.category_combo.currentText()
        if cat in self.rule_map:
            for rule in self.rule_map[cat]:
                if rule['name'] == name:
                    self.rule_id_edit.setText(str(rule['id']))
                    self.points_edit.setText(str(rule['points']))
                    break
    def validate_and_accept(self):
        record_no = self.record_no_edit.text().strip()
        resident_id = self.resident_id_edit.text().strip()
        rule_id = self.rule_id_edit.text().strip()
        points = self.points_edit.text().strip()
        operator_id = self.operator_id_edit.text().strip()
        if not record_no or not resident_id or not rule_id or not points or not operator_id:
            QMessageBox.warning(self, "校验", "带*为必填项！")
            return
        try:
            points_int = int(points)
            if points_int <= 0:
                raise ValueError
        except:
            QMessageBox.warning(self, "校验", "积分应为正整数！")
            return
        # 新增时校验编号唯一
        if self.record_no_edit.isEnabled():
            conn = sqlite3.connect(get_db_path())
            cur = conn.cursor()
            cur.execute("SELECT COUNT(*) FROM point_records WHERE record_no=?", (record_no,))
            if cur.fetchone()[0] > 0:
                QMessageBox.warning(self, "校验", "记录编号已存在！")
                conn.close()
                return
            conn.close()
        self.accept()
    def get_data(self):
        return {
            'record_no': self.record_no_edit.text().strip(),
            'resident_id': self.resident_id_edit.text().strip(),
            'rule_id': self.rule_id_edit.text().strip(),
            'points': self.points_edit.text().strip(),
            'record_time': self.record_time_edit.text().strip(),
            'operator_id': self.operator_id_edit.text().strip(),
            'status': self.status_combo.currentText(),
        }

# 积分记录页面
class PointRecordsPage(TablePage):
    def __init__(self, parent=None):
        headers = ["ID", "记录编号", "居民身份证", "描述", "积分", "记录时间", "操作人", "状态"]
        super().__init__(headers, parent)
        # 搜索栏和按钮
        top_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("居民身份证/记录编号")
        self.search_edit.setFixedWidth(220)
        self.search_btn = QPushButton("搜索")
        self.add_btn = QPushButton("新增")
        self.edit_btn = QPushButton("编辑")
        self.del_btn = QPushButton("删除")
        self.import_btn = QPushButton("导入Excel")
        self.export_btn = QPushButton("导出Excel")
        for btn in [self.search_btn, self.add_btn, self.edit_btn, self.del_btn, self.import_btn, self.export_btn]:
            btn.setStyleSheet("QPushButton {background: #409eff; color: white; border-radius: 6px; padding: 4px 14px; font-size: 14px;} QPushButton:hover {background: #66b1ff;}")
        self.del_btn.setStyleSheet("QPushButton {background: #f56c6c; color: white; border-radius: 6px; padding: 4px 14px; font-size: 14px;} QPushButton:hover {background: #ff8787;}")
        top_layout.addWidget(self.search_edit)
        top_layout.addWidget(self.search_btn)
        top_layout.addStretch()
        top_layout.addWidget(self.import_btn)
        top_layout.addWidget(self.export_btn)
        top_layout.addWidget(self.add_btn)
        top_layout.addWidget(self.edit_btn)
        top_layout.addWidget(self.del_btn)
        self.layout().insertLayout(0, top_layout)
        self.search_btn.clicked.connect(self.do_search)
        self.add_btn.clicked.connect(self.do_add)
        self.edit_btn.clicked.connect(self.do_edit)
        self.del_btn.clicked.connect(self.do_delete)
        self.import_btn.clicked.connect(self.do_import)
        self.export_btn.clicked.connect(self.do_export)

        # 连接快捷操作按钮
        self.quick_refresh_btn.clicked.connect(self.refresh)
        self.quick_add_btn.clicked.connect(self.do_add)
        self.quick_edit_btn.clicked.connect(self.do_edit)
        self.quick_delete_btn.clicked.connect(self.do_delete)

        self.refresh()
    def refresh(self, keyword=None):
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        if keyword:
            cur.execute("SELECT pr.id, pr.record_no, pr.resident_id, r.description, pr.points, pr.record_time, pr.operator_id, pr.status FROM point_records pr LEFT JOIN point_rules r ON pr.rule_id=r.id WHERE pr.resident_id LIKE ? OR pr.record_no LIKE ?", (f"%{keyword}%", f"%{keyword}%"))
        else:
            cur.execute("SELECT pr.id, pr.record_no, pr.resident_id, r.description, pr.points, pr.record_time, pr.operator_id, pr.status FROM point_records pr LEFT JOIN point_rules r ON pr.rule_id=r.id")
        data = cur.fetchall()
        conn.close()
        self.set_data(data)
    def do_search(self):
        keyword = self.search_edit.text().strip()
        self.refresh(keyword)
    def do_add(self):
        dlg = PointRecordEditDialog(self)
        if dlg.exec_() == QDialog.Accepted:
            d = dlg.get_data()
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                # 1. 校验居民是否存在
                cur.execute("SELECT COUNT(*) FROM residents WHERE id_card=?", (d['resident_id'],))
                if cur.fetchone()[0] == 0:
                    QMessageBox.warning(self, "错误", "该居民不存在，请先添加居民信息！")
                    conn.close()
                    return
                # 2. 校验规则有效期
                cur.execute("SELECT valid_from, valid_to, max_apply_times, apply_interval FROM point_rules WHERE id=?", (d['rule_id'],))
                rule = cur.fetchone()
                if not rule:
                    QMessageBox.warning(self, "错误", "所选积分项不存在！")
                    conn.close()
                    return
                valid_from, valid_to, max_apply_times, apply_interval = rule
                today = datetime.datetime.now().date()
                if valid_from:
                    valid_from_date = datetime.datetime.strptime(valid_from.split(' ')[0], '%Y-%m-%d').date()
                    if today < valid_from_date:
                        QMessageBox.warning(self, "错误", "该积分项尚未生效！")
                        conn.close()
                        return
                if valid_to and valid_to != '':
                    valid_to_date = datetime.datetime.strptime(valid_to.split(' ')[0], '%Y-%m-%d').date()
                    if today > valid_to_date:
                        QMessageBox.warning(self, "错误", "该积分项已过期！")
                        conn.close()
                        return
                # 3. 校验周期最大次数
                if max_apply_times and int(max_apply_times) > 0:
                    # 计算周期起止
                    if apply_interval == '日度':
                        start = today
                        end = today
                    elif apply_interval == '月度':
                        start = today.replace(day=1)
                        end = (today.replace(day=1) + datetime.timedelta(days=32)).replace(day=1) - datetime.timedelta(days=1)
                    elif apply_interval == '季度':
                        month = (today.month - 1) // 3 * 3 + 1
                        start = today.replace(month=month, day=1)
                        if month+3 > 12:
                            end = today.replace(year=today.year+1, month=1, day=1) - datetime.timedelta(days=1)
                        else:
                            end = today.replace(month=month+3, day=1) - datetime.timedelta(days=1)
                    elif apply_interval == '年度':
                        start = today.replace(month=1, day=1)
                        end = today.replace(month=12, day=31)
                    else:
                        start = end = today
                    cur.execute("SELECT COUNT(*) FROM point_records WHERE resident_id=? AND rule_id=? AND record_time BETWEEN ? AND ?", (d['resident_id'], d['rule_id'], str(start), str(end)))
                    count = cur.fetchone()[0]
                    if count >= int(max_apply_times):
                        QMessageBox.warning(self, "错误", f"该居民本{apply_interval}已达最大积分次数！")
                        conn.close()
                        return
                # 插入积分记录
                cur.execute("INSERT INTO point_records (record_no, resident_id, rule_id, points, record_time, operator_id, status) VALUES (?, ?, ?, ?, ?, ?, ?)",
                    (d['record_no'], d['resident_id'], d['rule_id'], d['points'], d['record_time'] or None, d['operator_id'], d['status']))
                conn.commit()
                log_operation('新增积分记录', f"居民:{d['resident_id']} 规则:{d['rule_id']} 积分:{d['points']}")
                conn.close()
                QMessageBox.information(self, "提示", "新增成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"新增失败：{e}")
    def do_edit(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要编辑的记录！")
            return
        row = selected[0].row()
        data = [self.table.item(row, i).text() for i in range(self.table.columnCount())]
        dlg = PointRecordEditDialog(self, data)
        if dlg.exec_() == QDialog.Accepted:
            d = dlg.get_data()
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("UPDATE point_records SET resident_id=?, rule_id=?, points=?, record_time=?, operator_id=?, status=? WHERE record_no=?",
                    (d['resident_id'], d['rule_id'], d['points'], d['record_time'] or None, d['operator_id'], d['status'], d['record_no']))
                conn.commit()
                conn.close()
                QMessageBox.information(self, "提示", "修改成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"修改失败：{e}")
    def do_delete(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要删除的记录！")
            return
        rows = set(item.row() for item in selected)
        record_nos = [self.table.item(row, 1).text() for row in rows]
        if QMessageBox.question(self, "确认", f"确定要删除选中的{len(record_nos)}条记录吗？", QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No) == QMessageBox.StandardButton.Yes:
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                # 检查是否被兑换记录引用（如有外键可加）
                # 直接删除
                cur.executemany("DELETE FROM point_records WHERE record_no=?", [(n,) for n in record_nos])
                conn.commit()
                log_operation('删除积分记录', f"记录编号:{','.join(record_nos)}")
                conn.close()
                QMessageBox.information(self, "提示", "删除成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除失败：{e}")
    def do_export(self):
        if openpyxl is None:
            QMessageBox.warning(self, "提示", "请先安装openpyxl库：pip install openpyxl")
            return
        path, _ = QFileDialog.getSaveFileName(self, "导出Excel", "积分记录.xlsx", "Excel Files (*.xlsx)")
        if not path:
            return
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.append([self.table.horizontalHeaderItem(i).text() for i in range(self.table.columnCount())])
        for row in range(self.table.rowCount()):
            ws.append([self.table.item(row, col).text() if self.table.item(row, col) else '' for col in range(self.table.columnCount())])
        try:
            wb.save(path)
            QMessageBox.information(self, "提示", f"导出成功：{os.path.basename(path)}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"导出失败：{e}")
    def do_import(self):
        if openpyxl is None:
            QMessageBox.warning(self, "提示", "请先安装openpyxl库：pip install openpyxl")
            return
        path, _ = QFileDialog.getOpenFileName(self, "导入Excel", "", "Excel Files (*.xlsx)")
        if not path:
            return
        wb = openpyxl.load_workbook(path)
        ws = wb.active
        rows = list(ws.iter_rows(min_row=2, values_only=True))
        success, fail = 0, 0
        for row in rows:
            try:
                d = {
                    'record_no': row[1],
                    'resident_id': row[2],
                    'rule_id': row[3],
                    'points': row[4],
                    'record_time': row[5],
                    'operator_id': row[6],
                    'status': row[7] if len(row) > 7 and row[7] else '有效',
                }
                if not d['record_no'] or not d['resident_id'] or not d['rule_id'] or not d['points'] or not d['operator_id']:
                    fail += 1; continue
                try:
                    points_int = int(d['points'])
                    if points_int <= 0:
                        fail += 1; continue
                except:
                    fail += 1; continue
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("SELECT COUNT(*) FROM point_records WHERE record_no=?", (d['record_no'],))
                if cur.fetchone()[0] > 0:
                    conn.close(); fail += 1; continue
                cur.execute("INSERT INTO point_records (record_no, resident_id, rule_id, points, record_time, operator_id, status) VALUES (?, ?, ?, ?, ?, ?, ?)",
                    (d['record_no'], d['resident_id'], d['rule_id'], d['points'], d['record_time'] or None, d['operator_id'], d['status']))
                conn.commit(); conn.close(); success += 1
            except Exception:
                fail += 1
        self.refresh()
        QMessageBox.information(self, "导入结果", f"成功导入{success}条，失败{fail}条。")

# 奖品编辑弹窗
class ItemEditDialog(QDialog):
    def __init__(self, parent=None, data=None):
        super().__init__(parent)
        self.setWindowTitle("奖品信息")
        self.setFixedSize(380, 260)
        self.setStyleSheet("QDialog {background: #fafdff; border-radius: 12px;}")
        layout = QFormLayout(self)
        font = QFont("微软雅黑", 11)
        self.setFont(font)
        self.name_edit = QLineEdit(); self.name_edit.setPlaceholderText("必填")
        self.points_edit = QLineEdit(); self.points_edit.setPlaceholderText("正整数")
        self.stock_edit = QLineEdit(); self.stock_edit.setPlaceholderText("0或正整数")
        self.remark_edit = QLineEdit(); self.remark_edit.setPlaceholderText("可选")
        for w in [self.name_edit, self.points_edit]:
            w.setStyleSheet("background: #fffbe6;")
        layout.setVerticalSpacing(12)
        layout.addRow("奖品名称*", self.name_edit)
        layout.addRow("所需积分*", self.points_edit)
        layout.addRow("库存", self.stock_edit)
        layout.addRow("备注", self.remark_edit)
        btn_layout = QHBoxLayout()
        self.ok_btn = QPushButton("确定")
        self.ok_btn.setStyleSheet("QPushButton {background: #409eff; color: white; border-radius: 6px; padding: 6px 18px; font-size: 15px;}")
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("QPushButton {background: #e0e0e0; border-radius: 6px; padding: 6px 18px; font-size: 15px;}")
        btn_layout.addStretch()
        btn_layout.addWidget(self.ok_btn)
        btn_layout.addWidget(self.cancel_btn)
        layout.addRow(btn_layout)
        self.ok_btn.clicked.connect(self.validate_and_accept)
        self.cancel_btn.clicked.connect(self.reject)
        if data:
            self.name_edit.setText(data[1])
            self.points_edit.setText(str(data[2]))
            self.stock_edit.setText(str(data[3]))
            self.remark_edit.setText(data[4])
            self.name_edit.setEnabled(False)
    def validate_and_accept(self):
        name = self.name_edit.text().strip()
        points = self.points_edit.text().strip()
        stock = self.stock_edit.text().strip() or '0'
        if not name or not points:
            QMessageBox.warning(self, "校验", "带*为必填项！")
            return
        try:
            points_int = int(points)
            if points_int <= 0:
                raise ValueError
        except:
            QMessageBox.warning(self, "校验", "所需积分应为正整数！")
            return
        try:
            stock_int = int(stock)
            if stock_int < 0:
                raise ValueError
        except:
            QMessageBox.warning(self, "校验", "库存应为0或正整数！")
            return
        # 新增时校验奖品名唯一
        if self.name_edit.isEnabled():
            conn = sqlite3.connect(get_db_path())
            cur = conn.cursor()
            cur.execute("SELECT COUNT(*) FROM exchange_items WHERE name=?", (name,))
            if cur.fetchone()[0] > 0:
                QMessageBox.warning(self, "校验", "奖品名称已存在！")
                conn.close()
                return
            conn.close()
        self.accept()
    def get_data(self):
        return {
            'name': self.name_edit.text().strip(),
            'points_needed': self.points_edit.text().strip(),
            'stock': self.stock_edit.text().strip() or '0',
            'remark': self.remark_edit.text().strip(),
        }

# 奖品管理页面
class ExchangeItemsPage(TablePage):
    def __init__(self, parent=None):
        headers = ["名称", "所需积分", "库存", "备注"]
        super().__init__(headers, parent)
        # 搜索栏和按钮
        top_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("奖品名称")
        self.search_edit.setFixedWidth(200)
        self.search_btn = QPushButton("搜索")
        self.add_btn = QPushButton("新增")
        self.edit_btn = QPushButton("编辑")
        self.del_btn = QPushButton("删除")
        self.import_btn = QPushButton("导入Excel")
        self.export_btn = QPushButton("导出Excel")
        for btn in [self.search_btn, self.add_btn, self.edit_btn, self.del_btn, self.import_btn, self.export_btn]:
            btn.setStyleSheet("QPushButton {background: #409eff; color: white; border-radius: 6px; padding: 4px 14px; font-size: 14px;} QPushButton:hover {background: #66b1ff;}")
        self.del_btn.setStyleSheet("QPushButton {background: #f56c6c; color: white; border-radius: 6px; padding: 4px 14px; font-size: 14px;} QPushButton:hover {background: #ff8787;}")
        top_layout.addWidget(self.search_edit)
        top_layout.addWidget(self.search_btn)
        top_layout.addStretch()
        top_layout.addWidget(self.import_btn)
        top_layout.addWidget(self.export_btn)
        top_layout.addWidget(self.add_btn)
        top_layout.addWidget(self.edit_btn)
        top_layout.addWidget(self.del_btn)
        self.layout().insertLayout(0, top_layout)
        self.search_btn.clicked.connect(self.do_search)
        self.add_btn.clicked.connect(self.do_add)
        self.edit_btn.clicked.connect(self.do_edit)
        self.del_btn.clicked.connect(self.do_delete)
        self.import_btn.clicked.connect(self.do_import)
        self.export_btn.clicked.connect(self.do_export)
        self.refresh()
        self.table.setStyleSheet("QTableWidget {background: #fff; font-size: 15px;} QHeaderView::section {font-weight: bold; background: #f0f0f0; font-size: 15px; color: #333;}")
        self.table.verticalHeader().setDefaultSectionSize(32)
    def refresh(self, keyword=None):
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        if keyword:
            cur.execute("SELECT name, points_needed, stock, remark FROM exchange_items WHERE name LIKE ?", (f"%{keyword}%",))
        else:
            cur.execute("SELECT name, points_needed, stock, remark FROM exchange_items")
        data = cur.fetchall()
        conn.close()
        self.set_data(data)
    def do_search(self):
        keyword = self.search_edit.text().strip()
        self.refresh(keyword)
    def do_add(self):
        dlg = ItemEditDialog(self)
        if dlg.exec_() == QDialog.Accepted:
            d = dlg.get_data()
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("INSERT INTO exchange_items (name, points_needed, stock, remark) VALUES (?, ?, ?, ?)",
                    (d['name'], d['points_needed'], d['stock'], d['remark']))
                conn.commit()
                conn.close()
                QMessageBox.information(self, "提示", "新增成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"新增失败：{e}")
    def do_edit(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要编辑的奖品！")
            return
        row = selected[0].row()
        data = [self.table.item(row, i).text() for i in range(self.table.columnCount())]
        dlg = ItemEditDialog(self, data)
        if dlg.exec_() == QDialog.Accepted:
            d = dlg.get_data()
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("UPDATE exchange_items SET points_needed=?, stock=?, remark=? WHERE name=?",
                    (d['points_needed'], d['stock'], d['remark'], d['name']))
                conn.commit()
                conn.close()
                QMessageBox.information(self, "提示", "修改成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"修改失败：{e}")
    def do_delete(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要删除的奖品！")
            return
        rows = set(item.row() for item in selected)
        names = [self.table.item(row, 0).text() for row in rows]
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        for name in names:
            cur.execute("SELECT COUNT(*) FROM exchange_records er LEFT JOIN exchange_items ei ON er.item_id=ei.id WHERE ei.name=?", (name,))
            if cur.fetchone()[0] > 0:
                QMessageBox.warning(self, "错误", f"奖品{name}已被兑换记录引用，禁止删除！")
                conn.close()
                return
        if QMessageBox.question(self, "确认", f"确定要删除选中的{len(names)}个奖品吗？", QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No) == QMessageBox.StandardButton.Yes:
            try:
                cur.executemany("DELETE FROM exchange_items WHERE name=?", [(n,) for n in names])
                conn.commit()
                log_operation('删除奖品', f"奖品:{','.join(names)}")
                conn.close()
                QMessageBox.information(self, "提示", "删除成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除失败：{e}")
    def do_export(self):
        if openpyxl is None:
            QMessageBox.warning(self, "提示", "请先安装openpyxl库：pip install openpyxl")
            return
        path, _ = QFileDialog.getSaveFileName(self, "导出Excel", "奖品信息.xlsx", "Excel Files (*.xlsx)")
        if not path:
            return
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.append([self.table.horizontalHeaderItem(i).text() for i in range(self.table.columnCount())])
        for row in range(self.table.rowCount()):
            ws.append([self.table.item(row, col).text() if self.table.item(row, col) else '' for col in range(self.table.columnCount())])
        try:
            wb.save(path)
            QMessageBox.information(self, "提示", f"导出成功：{os.path.basename(path)}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"导出失败：{e}")
    def do_import(self):
        if openpyxl is None:
            QMessageBox.warning(self, "提示", "请先安装openpyxl库：pip install openpyxl")
            return
        path, _ = QFileDialog.getOpenFileName(self, "导入Excel", "", "Excel Files (*.xlsx)")
        if not path:
            return
        wb = openpyxl.load_workbook(path)
        ws = wb.active
        rows = list(ws.iter_rows(min_row=2, values_only=True))
        success, fail = 0, 0
        for row in rows:
            try:
                d = {
                    'name': row[0],
                    'points_needed': row[1],
                    'stock': row[2] if row[2] is not None else 0,
                    'remark': row[3] if len(row) > 3 else '',
                }
                if not d['name'] or not d['points_needed']:
                    fail += 1; continue
                try:
                    points_int = int(d['points_needed'])
                    if points_int <= 0:
                        fail += 1; continue
                except:
                    fail += 1; continue
                try:
                    stock_int = int(d['stock'])
                    if stock_int < 0:
                        fail += 1; continue
                except:
                    fail += 1; continue
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("SELECT COUNT(*) FROM exchange_items WHERE name=?", (d['name'],))
                if cur.fetchone()[0] > 0:
                    conn.close(); fail += 1; continue
                cur.execute("INSERT INTO exchange_items (name, points_needed, stock, remark) VALUES (?, ?, ?, ?)",
                    (d['name'], d['points_needed'], d['stock'], d['remark']))
                conn.commit(); conn.close(); success += 1
            except Exception:
                fail += 1
        self.refresh()
        QMessageBox.information(self, "导入结果", f"成功导入{success}条，失败{fail}条。")

# 兑换记录编辑弹窗
class ExchangeRecordEditDialog(QDialog):
    def __init__(self, parent=None, data=None):
        super().__init__(parent)
        self.setWindowTitle("兑换记录")
        self.setFixedSize(480, 340)
        self.setStyleSheet("QDialog {background: #fafdff; border-radius: 12px;}")
        layout = QFormLayout(self)
        font = QFont("微软雅黑", 12)
        self.setFont(font)
        self.resident_id_edit = QLineEdit(); self.resident_id_edit.setPlaceholderText("居民身份证号")
        # 奖品下拉
        self.item_combo = QComboBox(); self.item_combo.setPlaceholderText("请选择奖品")
        self.item_id_edit = QLineEdit(); self.item_id_edit.setVisible(False)
        self.points_used_edit = QLineEdit(); self.points_used_edit.setPlaceholderText("正整数")
        # 兑换时间默认当前时间
        if data:
            date_val = data[4]
        else:
            date_val = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.date_edit = QLineEdit(date_val)
        self.date_edit.setPlaceholderText("YYYY-MM-DD HH:MM:SS")
        self.remark_edit = QLineEdit(); self.remark_edit.setPlaceholderText("可选")
        for w in [self.resident_id_edit, self.item_combo, self.points_used_edit]:
            w.setStyleSheet("background: #fffbe6;")
        layout.setVerticalSpacing(14)
        layout.addRow("居民身份证*", self.resident_id_edit)
        layout.addRow("奖品*", self.item_combo)
        layout.addRow("消耗积分*", self.points_used_edit)
        layout.addRow("兑换时间", self.date_edit)
        layout.addRow("备注", self.remark_edit)
        btn_layout = QHBoxLayout()
        self.ok_btn = QPushButton("确定")
        self.ok_btn.setStyleSheet("QPushButton {background: #409eff; color: white; border-radius: 6px; padding: 8px 24px; font-size: 16px;}")
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("QPushButton {background: #e0e0e0; border-radius: 6px; padding: 8px 24px; font-size: 16px;}")
        btn_layout.addStretch()
        btn_layout.addWidget(self.ok_btn)
        btn_layout.addSpacing(40)
        btn_layout.addWidget(self.cancel_btn)
        btn_layout.addStretch()
        layout.addRow(btn_layout)
        self.ok_btn.clicked.connect(self.validate_and_accept)
        self.cancel_btn.clicked.connect(self.reject)
        # 加载奖品数据
        self.item_map = {}  # {name: {id, points_needed}}
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute("SELECT id, name, points_needed FROM exchange_items")
        for iid, name, pts in cur.fetchall():
            self.item_map[name] = {'id': iid, 'points_needed': pts}
        conn.close()
        self.item_combo.addItems(self.item_map.keys())
        self.item_combo.currentTextChanged.connect(self.on_item_changed)
        # 编辑时回显
        if data:
            self.resident_id_edit.setText(data[1])
            # 反查奖品名
            item_id = data[2]
            for name, info in self.item_map.items():
                if str(info['id']) == str(item_id):
                    self.item_combo.setCurrentText(name)
                    self.on_item_changed(name)
                    break
            self.points_used_edit.setText(str(data[3]))
            self.date_edit.setText(data[4])
            self.remark_edit.setText(data[5])
        else:
            if self.item_combo.count() > 0:
                self.item_combo.setCurrentIndex(0)
                self.on_item_changed(self.item_combo.currentText())
        self.item_combo.setMinimumWidth(180)
    def on_item_changed(self, name):
        if name in self.item_map:
            self.item_id_edit.setText(str(self.item_map[name]['id']))
            self.points_used_edit.setText(str(self.item_map[name]['points_needed']))
    def validate_and_accept(self):
        resident_id = self.resident_id_edit.text().strip()
        item_id = self.item_id_edit.text().strip()
        points_used = self.points_used_edit.text().strip()
        if not resident_id or not item_id or not points_used:
            QMessageBox.warning(self, "校验", "带*为必填项！")
            return
        try:
            points_int = int(points_used)
            if points_int <= 0:
                raise ValueError
        except:
            QMessageBox.warning(self, "校验", "消耗积分应为正整数！")
            return
        self.accept()
    def get_data(self):
        return {
            'resident_id': self.resident_id_edit.text().strip(),
            'item_id': self.item_id_edit.text().strip(),
            'points_used': self.points_used_edit.text().strip(),
            'date': self.date_edit.text().strip(),
            'remark': self.remark_edit.text().strip(),
        }

# 兑换记录页面
class ExchangeRecordsPage(TablePage):
    def __init__(self, parent=None):
        headers = ["ID", "居民身份证", "名称", "消耗积分", "兑换时间", "备注"]
        super().__init__(headers, parent)
        # 搜索栏和按钮
        top_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("居民身份证/奖品名称")
        self.search_edit.setFixedWidth(220)
        self.search_btn = QPushButton("搜索")
        self.add_btn = QPushButton("新增")
        self.edit_btn = QPushButton("编辑")
        self.del_btn = QPushButton("删除")
        self.import_btn = QPushButton("导入Excel")
        self.export_btn = QPushButton("导出Excel")
        for btn in [self.search_btn, self.add_btn, self.edit_btn, self.del_btn, self.import_btn, self.export_btn]:
            btn.setStyleSheet("QPushButton {background: #409eff; color: white; border-radius: 6px; padding: 4px 14px; font-size: 14px;} QPushButton:hover {background: #66b1ff;}")
        self.del_btn.setStyleSheet("QPushButton {background: #f56c6c; color: white; border-radius: 6px; padding: 4px 14px; font-size: 14px;} QPushButton:hover {background: #ff8787;}")
        top_layout.addWidget(self.search_edit)
        top_layout.addWidget(self.search_btn)
        top_layout.addStretch()
        top_layout.addWidget(self.import_btn)
        top_layout.addWidget(self.export_btn)
        top_layout.addWidget(self.add_btn)
        top_layout.addWidget(self.edit_btn)
        top_layout.addWidget(self.del_btn)
        self.layout().insertLayout(0, top_layout)
        self.search_btn.clicked.connect(self.do_search)
        self.add_btn.clicked.connect(self.do_add)
        self.edit_btn.clicked.connect(self.do_edit)
        self.del_btn.clicked.connect(self.do_delete)
        self.import_btn.clicked.connect(self.do_import)
        self.export_btn.clicked.connect(self.do_export)
        self.refresh()
        # 表格美化
        self.table.setStyleSheet("QTableWidget {background: #fff; font-size: 15px;} QHeaderView::section {font-weight: bold; background: #f0f0f0; font-size: 15px; color: #333;}")
        self.table.verticalHeader().setDefaultSectionSize(32)
    def refresh(self, keyword=None):
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        if keyword:
            # 支持身份证或奖品名称模糊查找
            cur.execute("SELECT er.id, er.resident_id, ei.name, er.points_used, er.date, er.remark FROM exchange_records er LEFT JOIN exchange_items ei ON er.item_id=ei.id WHERE er.resident_id LIKE ? OR ei.name LIKE ?", (f"%{keyword}%", f"%{keyword}%"))
        else:
            cur.execute("SELECT er.id, er.resident_id, ei.name, er.points_used, er.date, er.remark FROM exchange_records er LEFT JOIN exchange_items ei ON er.item_id=ei.id")
        data = cur.fetchall()
        conn.close()
        self.set_data(data)
    def do_search(self):
        keyword = self.search_edit.text().strip()
        self.refresh(keyword)
    def do_add(self):
        dlg = ExchangeRecordEditDialog(self)
        if dlg.exec() == QDialog.DialogCode.Accepted:
            d = dlg.get_data()
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                # 查询该居民的有效积分总和
                cur.execute("SELECT COALESCE(SUM(points),0) FROM point_records WHERE resident_id=? AND status='有效'", (d['resident_id'],))
                total_points = cur.fetchone()[0]
                # 查询该居民已兑换消耗的积分
                cur.execute("SELECT COALESCE(SUM(points_used),0) FROM exchange_records WHERE resident_id=?", (d['resident_id'],))
                used_points = cur.fetchone()[0]
                available_points = total_points - used_points
                # 查询奖品所需积分和库存
                cur.execute("SELECT points_needed, stock FROM exchange_items WHERE id=?", (d['item_id'],))
                item_info = cur.fetchone()
                if not item_info:
                    raise Exception("奖品不存在")
                item_points, stock = item_info
                if available_points < int(item_points):
                    QMessageBox.warning(self, "积分不足", f"该居民可用积分为{available_points}，不足以兑换该奖品（需{item_points}积分）！")
                    conn.close()
                    return
                if stock <= 0:
                    QMessageBox.warning(self, "库存不足", f"奖品库存不足，无法兑换！")
                    conn.close()
                    return
                # 扣减库存
                cur.execute("UPDATE exchange_items SET stock=stock-1 WHERE id=?", (d['item_id'],))
                # 插入兑换记录
                cur.execute("INSERT INTO exchange_records (resident_id, item_id, points_used, date, remark) VALUES (?, ?, ?, ?, ?)",
                    (d['resident_id'], d['item_id'], d['points_used'], d['date'], d['remark']))
                conn.commit()
                log_operation('新增兑换记录', f"居民:{d['resident_id']} 奖品:{d['item_id']} 消耗:{d['points_used']}")
                conn.close()
                QMessageBox.information(self, "提示", "新增成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"新增失败：{e}")
    def do_edit(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要编辑的记录！")
            return
        row = selected[0].row()
        data = [self.table.item(row, i).text() for i in range(self.table.columnCount())]
        dlg = ExchangeRecordEditDialog(self, data)
        if dlg.exec_() == QDialog.Accepted:
            d = dlg.get_data()
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("UPDATE exchange_records SET resident_id=?, item_id=?, points_used=?, date=?, remark=? WHERE id=?",
                    (d['resident_id'], d['item_id'], d['points_used'], d['date'], d['remark'], data[0]))
                conn.commit()
                conn.close()
                QMessageBox.information(self, "提示", "修改成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"修改失败：{e}")
    def do_delete(self):
        selected = self.table.selectedItems()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择要删除的记录！")
            return
        rows = set(item.row() for item in selected)
        ids = [self.table.item(row, 0).text() for row in rows]
        if QMessageBox.question(self, "确认", f"确定要删除选中的{len(ids)}条记录吗？", QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No) == QMessageBox.StandardButton.Yes:
            try:
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.executemany("DELETE FROM exchange_records WHERE id=?", [(i,) for i in ids])
                conn.commit()
                log_operation('删除兑换记录', f"ID:{','.join(ids)}")
                conn.close()
                QMessageBox.information(self, "提示", "删除成功！")
                self.refresh()
            except Exception as e:
                QMessageBox.warning(self, "错误", f"删除失败：{e}")
    def do_export(self):
        if openpyxl is None:
            QMessageBox.warning(self, "提示", "请先安装openpyxl库：pip install openpyxl")
            return
        path, _ = QFileDialog.getSaveFileName(self, "导出Excel", "兑换记录.xlsx", "Excel Files (*.xlsx)")
        if not path:
            return
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.append([self.table.horizontalHeaderItem(i).text() for i in range(self.table.columnCount())])
        for row in range(self.table.rowCount()):
            ws.append([self.table.item(row, col).text() if self.table.item(row, col) else '' for col in range(self.table.columnCount())])
        try:
            wb.save(path)
            QMessageBox.information(self, "提示", f"导出成功：{os.path.basename(path)}")
        except Exception as e:
            QMessageBox.warning(self, "错误", f"导出失败：{e}")
    def do_import(self):
        if openpyxl is None:
            QMessageBox.warning(self, "提示", "请先安装openpyxl库：pip install openpyxl")
            return
        path, _ = QFileDialog.getOpenFileName(self, "导入Excel", "", "Excel Files (*.xlsx)")
        if not path:
            return
        wb = openpyxl.load_workbook(path)
        ws = wb.active
        rows = list(ws.iter_rows(min_row=2, values_only=True))
        success, fail = 0, 0
        for row in rows:
            try:
                d = {
                    'resident_id': row[1],
                    'item_id': row[2],
                    'points_used': row[3],
                    'date': row[4],
                    'remark': row[5] if len(row) > 5 else '',
                }
                if not d['resident_id'] or not d['item_id'] or not d['points_used']:
                    fail += 1; continue
                try:
                    points_int = int(d['points_used'])
                    if points_int <= 0:
                        fail += 1; continue
                except:
                    fail += 1; continue
                conn = sqlite3.connect(get_db_path())
                cur = conn.cursor()
                cur.execute("INSERT INTO exchange_records (resident_id, item_id, points_used, date, remark) VALUES (?, ?, ?, ?, ?)",
                    (d['resident_id'], d['item_id'], d['points_used'], d['date'], d['remark']))
                conn.commit(); conn.close(); success += 1
            except Exception:
                fail += 1
        self.refresh()
        QMessageBox.information(self, "导入结果", f"成功导入{success}条，失败{fail}条。")

# 左侧导航栏
class NavigationWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.setLayout(layout)
        self.menu_list = QListWidget()
        self.menu_list.addItems([
            "居民信息",
            "积分项",
            "积分记录",
            "奖品管理",
            "兑换记录"
        ])
        self.menu_list.setFixedWidth(180)
        self.menu_list.setStyleSheet('''
            QListWidget {font-size: 18px; font-family: 微软雅黑; background: #f7fafd; border: none;}
            QListWidget::item {height: 48px; text-align: center;}
            QListWidget::item:selected {background: #409eff; color: white; border-radius: 8px; margin: 4px;}
            QListWidget::item:hover {background: #e6f7ff;}
        ''')
        self.menu_list.setSpacing(4)
        self.menu_list.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.menu_list.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.menu_list.setMovement(QListWidget.Movement.Static)
        self.menu_list.setResizeMode(QListWidget.ResizeMode.Adjust)
        self.menu_list.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.menu_list.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.menu_list.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        # 分行标题
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(0)
        title1 = QLabel("伊州区东河街道")
        title2 = QLabel("卫生服务中心")
        title1.setFont(QFont("微软雅黑", 16, QFont.Weight.Bold))
        title2.setFont(QFont("微软雅黑", 16, QFont.Weight.Bold))
        title1.setStyleSheet("color: #409eff; text-align: center;")
        title2.setStyleSheet("color: #409eff; text-align: center;")
        title1.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title2.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_layout.addWidget(title1)
        title_layout.addWidget(title2)
        layout.addWidget(title_widget)
        layout.addWidget(self.menu_list, 1)
        layout.addStretch()
        self.setStyleSheet("background: #f7fafd; border-right: 1px solid #e0e0e0;")

# 主窗口
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("东河街道卫生服务中心积分兑换系统")
        self.resize(1280, 800)

        # 创建菜单栏
        self.create_menu_bar()

        # 主体布局
        main_widget = QWidget()
        main_layout = QHBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        main_widget.setStyleSheet("background: #f4f6fb;")
        self.setCentralWidget(main_widget)
        # 左侧导航
        self.nav = NavigationWidget()
        main_layout.addWidget(self.nav, 0)
        # 右侧功能区
        self.stack = QStackedWidget()
        self.pages = [
            ResidentsPage(),
            PointRulesPage(),
            PointRecordsPage(),
            ExchangeItemsPage(),
            ExchangeRecordsPage()
        ]
        for page in self.pages:
            self.stack.addWidget(page)
        main_layout.addWidget(self.stack, 1)
        # 导航栏切换
        self.nav.menu_list.currentRowChanged.connect(self.switch_page)
        self.nav.menu_list.setCurrentRow(0)
        # 美化主窗口
        self.setStyleSheet("QMainWindow {background: #f4f6fb;}")

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 系统菜单
        system_menu = menubar.addMenu('系统')

        # 数据库安全设置
        db_security_action = system_menu.addAction('🔒 数据库安全设置')
        db_security_action.triggered.connect(self.show_db_security_dialog)

        system_menu.addSeparator()

        # 关于
        about_action = system_menu.addAction('ℹ️ 关于系统')
        about_action.triggered.connect(self.show_about_dialog)

        # 退出
        exit_action = system_menu.addAction('🚪 退出系统')
        exit_action.triggered.connect(self.close)

    def show_db_security_dialog(self):
        """显示数据库安全设置对话框"""
        dialog = DatabaseSecurityDialog(self)
        dialog.exec_()

    def show_about_dialog(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于系统",
            "伊州区东河街道卫生服务中心积分兑换系统\n\n"
            "版本: v1.0\n"
            "开发时间: 2024年\n"
            "功能: 居民积分管理、奖品兑换\n\n"
            "🔒 数据库已启用安全保护")

    def switch_page(self, index):
        self.stack.setCurrentIndex(index)
        if hasattr(self.pages[index], 'refresh'):
            self.pages[index].refresh()

def log_operation(op_type, op_desc):
    try:
        conn = get_secure_connection()
        cur = conn.cursor()
        cur.execute("INSERT INTO operation_logs (op_type, op_desc) VALUES (?, ?)", (op_type, op_desc))
        conn.commit()
        conn.close()
    except PermissionError:
        # 日志记录失败不应该阻止主要操作
        pass
    except Exception:
        # 日志记录失败不应该阻止主要操作
        pass

# 数据库安全设置对话框
class DatabaseSecurityDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("数据库安全设置")
        self.setFixedSize(450, 300)
        self.setStyleSheet("""
            QDialog {background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #f0f8ff, stop:1 #e6f3ff); border-radius: 12px; border: 2px solid #409eff;}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 标题
        title = QLabel("数据库安全设置")
        title.setFont(QFont("微软雅黑", 18, QFont.Weight.Bold))
        title.setStyleSheet("color: #409eff;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 说明文本
        info_label = QLabel("为了保护您的数据安全，数据库访问需要密码验证。\n数据库密码与登录密码相同，确保数据安全。")
        info_label.setFont(QFont("微软雅黑", 11))
        info_label.setStyleSheet("color: #666; line-height: 1.5;")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 当前状态
        status_layout = QHBoxLayout()
        status_label = QLabel("当前状态:")
        status_label.setFont(QFont("微软雅黑", 12))
        self.status_value = QLabel("✅ 数据库已受保护" if verify_db_access() else "❌ 数据库未受保护")
        self.status_value.setFont(QFont("微软雅黑", 12))
        self.status_value.setStyleSheet("color: #67c23a;" if verify_db_access() else "color: #f56c6c;")
        status_layout.addWidget(status_label)
        status_layout.addWidget(self.status_value)
        status_layout.addStretch()
        layout.addLayout(status_layout)

        # 按钮
        btn_layout = QHBoxLayout()
        self.test_btn = QPushButton("测试连接")
        self.test_btn.setStyleSheet("""
            QPushButton {background: #409eff; color: white; border-radius: 8px; padding: 8px 20px; font-size: 14px;}
            QPushButton:hover {background: #66b1ff;}
        """)

        self.close_btn = QPushButton("关闭")
        self.close_btn.setStyleSheet("""
            QPushButton {background: #e0e0e0; border-radius: 8px; padding: 8px 20px; font-size: 14px;}
            QPushButton:hover {background: #d0d0d0;}
        """)

        btn_layout.addStretch()
        btn_layout.addWidget(self.test_btn)
        btn_layout.addWidget(self.close_btn)
        layout.addLayout(btn_layout)

        # 连接信号
        self.test_btn.clicked.connect(self.test_connection)
        self.close_btn.clicked.connect(self.accept)

    def test_connection(self):
        try:
            conn = get_secure_connection()
            cur = conn.cursor()
            cur.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cur.fetchone()[0]
            conn.close()
            QMessageBox.information(self, "连接测试", f"✅ 数据库连接成功！\n发现 {table_count} 个数据表。")
        except PermissionError:
            QMessageBox.warning(self, "连接测试", "❌ 数据库访问权限验证失败！")
        except Exception as e:
            QMessageBox.warning(self, "连接测试", f"❌ 数据库连接失败：{e}")

class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("登录")
        self.setFixedSize(400, 420)
        self.setStyleSheet("""
            QDialog {background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0a183d, stop:1 #122b54); border-radius: 18px; border: 2px solid #1ecfff;}
        """)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(32, 32, 32, 32)
        layout.setSpacing(18)
        # 标题
        title = QLabel("欢迎登录")
        title.setFont(QFont("微软雅黑", 26, QFont.Weight.Bold))
        title.setStyleSheet("color: #1ecfff; letter-spacing: 8px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        # 输入框区
        form = QFormLayout()
        form.setSpacing(18)
        # 用户名输入框
        self.user_edit = QLineEdit(); self.user_edit.setPlaceholderText("请输入用户名")
        self.user_edit.setStyleSheet('''
            QLineEdit {background: #0a183d; color: #fff; border: 2px solid #1ecfff; border-radius: 8px; padding-left: 36px; height: 38px; font-size: 16px;}
            QLineEdit:focus {border: 2px solid #00eaff; background: #122b54;}
        ''')
        user_icon = QLabel()
        user_icon.setPixmap(QPixmap(16, 16))
        user_icon.setStyleSheet('background: transparent;')
        user_icon.setFixedWidth(24)
        user_icon.setText("🧑")
        user_box = QHBoxLayout()
        user_box.addWidget(user_icon)
        user_box.addWidget(self.user_edit)
        user_box.setContentsMargins(0,0,0,0)
        user_box.setSpacing(0)
        user_widget = QWidget(); user_widget.setLayout(user_box)
        # 密码输入框
        self.pwd_edit = QLineEdit(); self.pwd_edit.setPlaceholderText("请输入密码"); self.pwd_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.pwd_edit.setStyleSheet('''
            QLineEdit {background: #0a183d; color: #fff; border: 2px solid #1ecfff; border-radius: 8px; padding-left: 36px; height: 38px; font-size: 16px;}
            QLineEdit:focus {border: 2px solid #00eaff; background: #122b54;}
        ''')
        pwd_icon = QLabel()
        pwd_icon.setPixmap(QPixmap(16, 16))
        pwd_icon.setStyleSheet('background: transparent;')
        pwd_icon.setFixedWidth(24)
        pwd_icon.setText("🔒")
        pwd_box = QHBoxLayout()
        pwd_box.addWidget(pwd_icon)
        pwd_box.addWidget(self.pwd_edit)
        pwd_box.setContentsMargins(0,0,0,0)
        pwd_box.setSpacing(0)
        pwd_widget = QWidget(); pwd_widget.setLayout(pwd_box)
        form.addRow(user_widget)
        form.addRow(pwd_widget)
        layout.addLayout(form)
        # 下方辅助链接
        link_box = QHBoxLayout()
        phone_link = QLabel('<a style="color:#1ecfff;" href="#">ddjd</a>')
        phone_link.setFont(QFont("微软雅黑", 10))
        phone_link.setAlignment(Qt.AlignmentFlag.AlignLeft)
        pwd_link = QLabel('<a style="color:#1ecfff;" href="#">1234</a>')
        pwd_link.setFont(QFont("微软雅黑", 10))
        pwd_link.setAlignment(Qt.AlignmentFlag.AlignRight)
        link_box.addWidget(phone_link)
        link_box.addStretch()
        link_box.addWidget(pwd_link)
        layout.addLayout(link_box)
        # 登录按钮
        btn_layout = QHBoxLayout()
        self.login_btn = QPushButton("登录")
        self.login_btn.setStyleSheet('''
            QPushButton {background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1ecfff, stop:1 #00eaff); color: #fff; border-radius: 12px; font-size: 20px; padding: 10px 0; min-width: 160px;}
            QPushButton:hover {background: #00eaff;}
        ''')
        btn_layout.addStretch()
        btn_layout.addWidget(self.login_btn)
        btn_layout.addStretch()
        layout.addSpacing(10)
        layout.addLayout(btn_layout)
        self.login_btn.clicked.connect(self.try_login)
        self.user_edit.returnPressed.connect(self.try_login)
        self.pwd_edit.returnPressed.connect(self.try_login)
    def try_login(self):
        user = self.user_edit.text().strip()
        pwd = self.pwd_edit.text().strip()
        if user == 'admin' and pwd == 'ddjd1234':
            # 登录成功后设置数据库密码
            set_db_password(pwd)
            self.accept()
        else:
            QMessageBox.warning(self, "登录失败", "账号或密码错误！")

# 数据库初始化函数
def init_db(db_path='points_exchange.db'):
    sql_script = """
    PRAGMA foreign_keys = ON;
    CREATE TABLE IF NOT EXISTS residents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        id_card TEXT NOT NULL UNIQUE,
        gender TEXT CHECK(gender IN ('男', '女', '其他')),
        age INTEGER CHECK(age BETWEEN 0 AND 120),
        phone TEXT CHECK(LENGTH(phone) = 11),
        group_type TEXT CHECK(group_type IN ('所有人','0-6岁儿童','孕产妇','老年人','慢性病','严重精神障碍','肺结核')),
        doctor_phone TEXT,
        address TEXT,
        remark TEXT
    );
    CREATE TABLE IF NOT EXISTS point_rules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        rule_code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        points INTEGER NOT NULL CHECK(points > 0),
        description TEXT,
        valid_from DATE NOT NULL,
        valid_to DATE,
        max_apply_times INTEGER DEFAULT 0,
        apply_interval TEXT CHECK(apply_interval IN ('日度','月度','季度','年度'))
    );
    CREATE TABLE IF NOT EXISTS point_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        record_no TEXT NOT NULL UNIQUE,
        resident_id TEXT NOT NULL,
        rule_id INTEGER NOT NULL,
        points INTEGER NOT NULL CHECK(points > 0),
        record_time DATE DEFAULT CURRENT_DATE,
        operator_id TEXT NOT NULL,
        status TEXT DEFAULT '有效' CHECK(status IN ('有效','已过期','已冻结')),
        FOREIGN KEY (resident_id) REFERENCES residents(id_card),
        FOREIGN KEY (rule_id) REFERENCES point_rules(id)
    );
    CREATE TABLE IF NOT EXISTS exchange_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        points_needed INTEGER NOT NULL CHECK(points_needed > 0),
        stock INTEGER DEFAULT 0 CHECK(stock >= 0),
        remark TEXT
    );
    CREATE TABLE IF NOT EXISTS exchange_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resident_id TEXT NOT NULL,
        item_id INTEGER NOT NULL,
        points_used INTEGER NOT NULL CHECK(points_used > 0),
        date DATETIME DEFAULT CURRENT_TIMESTAMP,
        remark TEXT,
        FOREIGN KEY (resident_id) REFERENCES residents(id_card),
        FOREIGN KEY (item_id) REFERENCES exchange_items(id)
    );
    CREATE INDEX IF NOT EXISTS idx_residents_idcard ON residents(id_card);
    CREATE INDEX IF NOT EXISTS idx_point_records_resident ON point_records(resident_id);
    CREATE INDEX IF NOT EXISTS idx_exchange_records_date ON exchange_records(date);
    """
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.executescript(sql_script)
    conn.commit()
    conn.close()

if __name__ == "__main__":
    import sys

    # 设置数据库路径
    set_db_path()

    app = QApplication(sys.argv)

    # 初始化数据库（如果不存在）
    if not os.path.exists(get_db_path()):
        try:
            init_db(get_db_path())
        except Exception as e:
            QMessageBox.critical(None, "数据库初始化失败", f"数据库初始化失败：{e}")
            sys.exit(1)

    # 创建日志表（如果不存在）
    try:
        conn = sqlite3.connect(get_db_path())
        cur = conn.cursor()
        cur.execute('''CREATE TABLE IF NOT EXISTS operation_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            op_type TEXT,
            op_desc TEXT,
            op_time DATETIME DEFAULT CURRENT_TIMESTAMP
        )''')
        conn.commit()
        conn.close()
    except Exception as e:
        QMessageBox.critical(None, "日志表初始化失败", f"日志表初始化失败：{e}")
        sys.exit(1)

    # 显示登录对话框
    login = LoginDialog()
    if login.exec_() == QDialog.Accepted:
        window = MainWindow()
        window.show()
        sys.exit(app.exec_())
    else:
        sys.exit(0)