========================================
伊州区东河街道卫生服务中心积分兑换系统
部署包说明
========================================

📦 打包完成信息
----------------------------------------
✅ 打包状态: 成功
📁 文件位置: dist/积分兑换系统.exe
📏 文件大小: 62.4 MB
🖥️ 系统要求: Windows 10/11 (64位)

🔧 打包配置
----------------------------------------
- 单文件打包 (--onefile)
- 无控制台窗口 (--windowed)
- 包含医院图标 (hospital.ico)
- 内置数据库文件
- 程序名称: 积分兑换系统

🚀 使用说明
----------------------------------------
1. 将 "积分兑换系统.exe" 复制到目标电脑
2. 双击运行，无需安装Python环境
3. 首次运行会自动创建数据库
4. 使用以下账号登录:
   用户名: admin
   密码: ddjd1234

📋 主要功能
----------------------------------------
✓ 居民信息管理 (增删改查)
✓ 积分规则设置
✓ 积分记录管理
✓ 奖品库存管理
✓ 兑换记录查询
✓ Excel数据导入导出
✓ 操作日志记录

🔒 安全特性
----------------------------------------
- 登录验证机制
- 数据库自动备份
- 操作日志记录
- 数据完整性检查

💾 数据存储
----------------------------------------
- 数据库文件: points_exchange.db
- 自动在程序目录创建
- 包含完整的示例数据
- 支持数据导入导出

⚠️ 注意事项
----------------------------------------
1. 程序需要在其所在目录有读写权限
2. 建议定期备份数据库文件
3. 如被防火墙拦截，请添加到信任列表
4. 首次运行可能需要几秒钟初始化

🛠️ 故障排除
----------------------------------------
问题: 程序无法启动
解决: 检查磁盘空间和权限，尝试以管理员身份运行

问题: 登录失败
解决: 确认用户名密码 (admin/ddjd1234)

问题: 数据丢失
解决: 查看程序目录下的 points_exchange.db 文件

📞 技术支持
----------------------------------------
如遇问题请提供:
- 错误信息截图
- 操作系统版本
- 具体操作步骤

========================================
版本信息: v1.0
打包时间: 2024年
开发环境: Python 3.10 + PyQt5
========================================
