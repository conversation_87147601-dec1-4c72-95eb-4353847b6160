('C:\\Users\\<USER>\\Desktop\\伊州区东河街道卫生服务中心积分兑换系统\\build\\积分兑换系统_压缩版\\PYZ-00.pyz',
 [('PIL', 'D:\\py3.10\\lib\\site-packages\\PIL\\__init__.py', 'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\py3.10\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\py3.10\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image', 'D:\\py3.10\\lib\\site-packages\\PIL\\Image.py', 'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\py3.10\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\py3.10\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\py3.10\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\py3.10\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\py3.10\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\py3.10\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\py3.10\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\py3.10\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._util', 'D:\\py3.10\\lib\\site-packages\\PIL\\_util.py', 'PYMODULE'),
  ('PIL._version',
   'D:\\py3.10\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\py3.10\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5', 'D:\\py3.10\\lib\\site-packages\\PyQt5\\__init__.py', 'PYMODULE'),
  ('__future__', 'D:\\py3.10\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\py3.10\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess', 'D:\\py3.10\\lib\\_bootsubprocess.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\py3.10\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\py3.10\\lib\\_compression.py', 'PYMODULE'),
  ('_distutils_hack',
   'D:\\py3.10\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\py3.10\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_markupbase', 'D:\\py3.10\\lib\\_markupbase.py', 'PYMODULE'),
  ('_osx_support', 'D:\\py3.10\\lib\\_osx_support.py', 'PYMODULE'),
  ('_py_abc', 'D:\\py3.10\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\py3.10\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\py3.10\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\py3.10\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_sitebuiltins', 'D:\\py3.10\\lib\\_sitebuiltins.py', 'PYMODULE'),
  ('_strptime', 'D:\\py3.10\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\py3.10\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\py3.10\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\py3.10\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\py3.10\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\py3.10\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\py3.10\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\py3.10\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks', 'D:\\py3.10\\lib\\asyncio\\base_tasks.py', 'PYMODULE'),
  ('asyncio.constants', 'D:\\py3.10\\lib\\asyncio\\constants.py', 'PYMODULE'),
  ('asyncio.coroutines', 'D:\\py3.10\\lib\\asyncio\\coroutines.py', 'PYMODULE'),
  ('asyncio.events', 'D:\\py3.10\\lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.exceptions', 'D:\\py3.10\\lib\\asyncio\\exceptions.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\py3.10\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures', 'D:\\py3.10\\lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\py3.10\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\py3.10\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins', 'D:\\py3.10\\lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\py3.10\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols', 'D:\\py3.10\\lib\\asyncio\\protocols.py', 'PYMODULE'),
  ('asyncio.queues', 'D:\\py3.10\\lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'D:\\py3.10\\lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\py3.10\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto', 'D:\\py3.10\\lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.staggered', 'D:\\py3.10\\lib\\asyncio\\staggered.py', 'PYMODULE'),
  ('asyncio.streams', 'D:\\py3.10\\lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.subprocess', 'D:\\py3.10\\lib\\asyncio\\subprocess.py', 'PYMODULE'),
  ('asyncio.tasks', 'D:\\py3.10\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads', 'D:\\py3.10\\lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.transports', 'D:\\py3.10\\lib\\asyncio\\transports.py', 'PYMODULE'),
  ('asyncio.trsock', 'D:\\py3.10\\lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\py3.10\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\py3.10\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\py3.10\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\py3.10\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\py3.10\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\py3.10\\lib\\bisect.py', 'PYMODULE'),
  ('bs4', 'D:\\py3.10\\lib\\site-packages\\bs4\\__init__.py', 'PYMODULE'),
  ('bs4.builder',
   'D:\\py3.10\\lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\py3.10\\lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\py3.10\\lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\py3.10\\lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css', 'D:\\py3.10\\lib\\site-packages\\bs4\\css.py', 'PYMODULE'),
  ('bs4.dammit', 'D:\\py3.10\\lib\\site-packages\\bs4\\dammit.py', 'PYMODULE'),
  ('bs4.element',
   'D:\\py3.10\\lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\py3.10\\lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'D:\\py3.10\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\py3.10\\lib\\calendar.py', 'PYMODULE'),
  ('cffi', 'D:\\py3.10\\lib\\site-packages\\cffi\\__init__.py', 'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\py3.10\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\py3.10\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api', 'D:\\py3.10\\lib\\site-packages\\cffi\\api.py', 'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\py3.10\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\py3.10\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\py3.10\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error', 'D:\\py3.10\\lib\\site-packages\\cffi\\error.py', 'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\py3.10\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock', 'D:\\py3.10\\lib\\site-packages\\cffi\\lock.py', 'PYMODULE'),
  ('cffi.model', 'D:\\py3.10\\lib\\site-packages\\cffi\\model.py', 'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\py3.10\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\py3.10\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\py3.10\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\py3.10\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\py3.10\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'D:\\py3.10\\lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'D:\\py3.10\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\py3.10\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\py3.10\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\py3.10\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'D:\\py3.10\\lib\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\py3.10\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\py3.10\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\py3.10\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\py3.10\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\py3.10\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\py3.10\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'D:\\py3.10\\lib\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\py3.10\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\py3.10\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\py3.10\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\py3.10\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\py3.10\\lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\py3.10\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\py3.10\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\py3.10\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'D:\\py3.10\\lib\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\py3.10\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\py3.10\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\py3.10\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\py3.10\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\py3.10\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\py3.10\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\py3.10\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\py3.10\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\py3.10\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\py3.10\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\py3.10\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd', 'D:\\py3.10\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\py3.10\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\py3.10\\lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'D:\\py3.10\\lib\\colorsys.py', 'PYMODULE'),
  ('commctrl',
   'D:\\py3.10\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent', 'D:\\py3.10\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\py3.10\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\py3.10\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\py3.10\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\py3.10\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\py3.10\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\py3.10\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\py3.10\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\py3.10\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\py3.10\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\py3.10\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix', 'D:\\py3.10\\lib\\ctypes\\_aix.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\py3.10\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('ctypes.macholib',
   'D:\\py3.10\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\py3.10\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\py3.10\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\py3.10\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util', 'D:\\py3.10\\lib\\ctypes\\util.py', 'PYMODULE'),
  ('dataclasses', 'D:\\py3.10\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\py3.10\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\py3.10\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\py3.10\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\py3.10\\lib\\dis.py', 'PYMODULE'),
  ('distutils', 'D:\\py3.10\\lib\\distutils\\__init__.py', 'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\py3.10\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\py3.10\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\py3.10\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd', 'D:\\py3.10\\lib\\distutils\\cmd.py', 'PYMODULE'),
  ('distutils.command',
   'D:\\py3.10\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\py3.10\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\py3.10\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\py3.10\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\py3.10\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config', 'D:\\py3.10\\lib\\distutils\\config.py', 'PYMODULE'),
  ('distutils.core', 'D:\\py3.10\\lib\\distutils\\core.py', 'PYMODULE'),
  ('distutils.debug', 'D:\\py3.10\\lib\\distutils\\debug.py', 'PYMODULE'),
  ('distutils.dep_util', 'D:\\py3.10\\lib\\distutils\\dep_util.py', 'PYMODULE'),
  ('distutils.dir_util', 'D:\\py3.10\\lib\\distutils\\dir_util.py', 'PYMODULE'),
  ('distutils.dist', 'D:\\py3.10\\lib\\distutils\\dist.py', 'PYMODULE'),
  ('distutils.errors', 'D:\\py3.10\\lib\\distutils\\errors.py', 'PYMODULE'),
  ('distutils.extension',
   'D:\\py3.10\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\py3.10\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\py3.10\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist', 'D:\\py3.10\\lib\\distutils\\filelist.py', 'PYMODULE'),
  ('distutils.log', 'D:\\py3.10\\lib\\distutils\\log.py', 'PYMODULE'),
  ('distutils.msvc9compiler',
   'D:\\py3.10\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn', 'D:\\py3.10\\lib\\distutils\\spawn.py', 'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\py3.10\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\py3.10\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.unixccompiler',
   'D:\\py3.10\\lib\\distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.util', 'D:\\py3.10\\lib\\distutils\\util.py', 'PYMODULE'),
  ('distutils.version', 'D:\\py3.10\\lib\\distutils\\version.py', 'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\py3.10\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest', 'D:\\py3.10\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\py3.10\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\py3.10\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\py3.10\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\py3.10\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase', 'D:\\py3.10\\lib\\email\\_policybase.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\py3.10\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\py3.10\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\py3.10\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\py3.10\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\py3.10\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\py3.10\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\py3.10\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\py3.10\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\py3.10\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\py3.10\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\py3.10\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\py3.10\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\py3.10\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\py3.10\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\py3.10\\lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'D:\\py3.10\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\py3.10\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\py3.10\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\py3.10\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\py3.10\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\py3.10\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\py3.10\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\py3.10\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\py3.10\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\py3.10\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\py3.10\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\py3.10\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\py3.10\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\py3.10\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\py3.10\\lib\\html\\entities.py', 'PYMODULE'),
  ('html.parser', 'D:\\py3.10\\lib\\html\\parser.py', 'PYMODULE'),
  ('http', 'D:\\py3.10\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\py3.10\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar', 'D:\\py3.10\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http.server', 'D:\\py3.10\\lib\\http\\server.py', 'PYMODULE'),
  ('imp', 'D:\\py3.10\\lib\\imp.py', 'PYMODULE'),
  ('importlib', 'D:\\py3.10\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\py3.10\\lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._adapters',
   'D:\\py3.10\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\py3.10\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\py3.10\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common', 'D:\\py3.10\\lib\\importlib\\_common.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\py3.10\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\py3.10\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\py3.10\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\py3.10\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\py3.10\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\py3.10\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\py3.10\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\py3.10\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\py3.10\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers', 'D:\\py3.10\\lib\\importlib\\readers.py', 'PYMODULE'),
  ('importlib.resources',
   'D:\\py3.10\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\py3.10\\lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\py3.10\\lib\\inspect.py', 'PYMODULE'),
  ('jinja2', 'D:\\py3.10\\lib\\site-packages\\jinja2\\__init__.py', 'PYMODULE'),
  ('jinja2._identifier',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext', 'D:\\py3.10\\lib\\site-packages\\jinja2\\ext.py', 'PYMODULE'),
  ('jinja2.filters',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\py3.10\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'D:\\py3.10\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\py3.10\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\py3.10\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\py3.10\\lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\py3.10\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lxml', 'D:\\py3.10\\lib\\site-packages\\lxml\\__init__.py', 'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\py3.10\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'D:\\py3.10\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\py3.10\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\py3.10\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\py3.10\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\py3.10\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\py3.10\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\py3.10\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\py3.10\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\py3.10\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\py3.10\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\py3.10\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'D:\\py3.10\\lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\py3.10\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\py3.10\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\py3.10\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\py3.10\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\py3.10\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\py3.10\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\py3.10\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\py3.10\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\py3.10\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\py3.10\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\py3.10\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\py3.10\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\py3.10\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\py3.10\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\py3.10\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\py3.10\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\py3.10\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\py3.10\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\py3.10\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\py3.10\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\py3.10\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\py3.10\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\py3.10\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\py3.10\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\py3.10\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\py3.10\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\py3.10\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\py3.10\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\py3.10\\lib\\numbers.py', 'PYMODULE'),
  ('numpy', 'D:\\py3.10\\lib\\site-packages\\numpy\\__init__.py', 'PYMODULE'),
  ('numpy.__config__',
   'D:\\py3.10\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\py3.10\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\py3.10\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\py3.10\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\py3.10\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\py3.10\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\py3.10\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\py3.10\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\py3.10\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\py3.10\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\py3.10\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\py3.10\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\py3.10\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\py3.10\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\py3.10\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\py3.10\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\py3.10\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\py3.10\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\py3.10\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\py3.10\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\py3.10\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\py3.10\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\py3.10\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\py3.10\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\py3.10\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\py3.10\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\py3.10\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\py3.10\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\py3.10\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\py3.10\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\py3.10\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\py3.10\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\py3.10\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\py3.10\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\py3.10\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\py3.10\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\py3.10\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\py3.10\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\py3.10\\lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\py3.10\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'D:\\py3.10\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'D:\\py3.10\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\py3.10\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\py3.10\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\py3.10\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\py3.10\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\py3.10\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\py3.10\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\py3.10\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\py3.10\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\py3.10\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\py3.10\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\py3.10\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\py3.10\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\py3.10\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\py3.10\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\py3.10\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\py3.10\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\py3.10\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\py3.10\\lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.actions',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.common',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.core',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.diagram',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.exceptions',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.helpers',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.results',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.testing',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.unicode',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing.util',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\py3.10\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\py3.10\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\py3.10\\lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'D:\\py3.10\\lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'D:\\py3.10\\lib\\pprint.py', 'PYMODULE'),
  ('psutil', 'D:\\py3.10\\lib\\site-packages\\psutil\\__init__.py', 'PYMODULE'),
  ('psutil._common',
   'D:\\py3.10\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._compat',
   'D:\\py3.10\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\py3.10\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'D:\\py3.10\\lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'D:\\py3.10\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\py3.10\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\py3.10\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\py3.10\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\py3.10\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\py3.10\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\py3.10\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\py3.10\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\py3.10\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\py3.10\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\py3.10\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'D:\\py3.10\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data', 'D:\\py3.10\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('pydoc_data.topics', 'D:\\py3.10\\lib\\pydoc_data\\topics.py', 'PYMODULE'),
  ('pyparsing',
   'D:\\py3.10\\lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'D:\\py3.10\\lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'D:\\py3.10\\lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'D:\\py3.10\\lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'D:\\py3.10\\lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'D:\\py3.10\\lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'D:\\py3.10\\lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'D:\\py3.10\\lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'D:\\py3.10\\lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'D:\\py3.10\\lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'D:\\py3.10\\lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pythoncom', 'D:\\py3.10\\lib\\site-packages\\pythoncom.py', 'PYMODULE'),
  ('pywin',
   'D:\\py3.10\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'D:\\py3.10\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'D:\\py3.10\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'D:\\py3.10\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'D:\\py3.10\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'D:\\py3.10\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'D:\\py3.10\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'D:\\py3.10\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'D:\\py3.10\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'D:\\py3.10\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue', 'D:\\py3.10\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\py3.10\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\py3.10\\lib\\random.py', 'PYMODULE'),
  ('rlcompleter', 'D:\\py3.10\\lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'D:\\py3.10\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\py3.10\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\py3.10\\lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.actions',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\actions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.common',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\common.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.core',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\core.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.diagram',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.exceptions',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.helpers',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.results',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\results.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.testing',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\testing.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.unicode',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing.util',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\pyparsing\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\py3.10\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\py3.10\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\py3.10\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\py3.10\\lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\py3.10\\lib\\site.py', 'PYMODULE'),
  ('socket', 'D:\\py3.10\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\py3.10\\lib\\socketserver.py', 'PYMODULE'),
  ('soupsieve',
   'D:\\py3.10\\lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\py3.10\\lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\py3.10\\lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\py3.10\\lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\py3.10\\lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\py3.10\\lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\py3.10\\lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3', 'D:\\py3.10\\lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'D:\\py3.10\\lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('sqlite3.dump', 'D:\\py3.10\\lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'D:\\py3.10\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\py3.10\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\py3.10\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\py3.10\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\py3.10\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\py3.10\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\py3.10\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\py3.10\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\py3.10\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\py3.10\\lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\py3.10\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\py3.10\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\py3.10\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\py3.10\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\py3.10\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\py3.10\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest', 'D:\\py3.10\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'D:\\py3.10\\lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\py3.10\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\py3.10\\lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader', 'D:\\py3.10\\lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.main', 'D:\\py3.10\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.mock', 'D:\\py3.10\\lib\\unittest\\mock.py', 'PYMODULE'),
  ('unittest.result', 'D:\\py3.10\\lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.runner', 'D:\\py3.10\\lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.signals', 'D:\\py3.10\\lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.suite', 'D:\\py3.10\\lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.util', 'D:\\py3.10\\lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'D:\\py3.10\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\py3.10\\lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\py3.10\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request', 'D:\\py3.10\\lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib.response', 'D:\\py3.10\\lib\\urllib\\response.py', 'PYMODULE'),
  ('uu', 'D:\\py3.10\\lib\\uu.py', 'PYMODULE'),
  ('webbrowser', 'D:\\py3.10\\lib\\webbrowser.py', 'PYMODULE'),
  ('win32com',
   'D:\\py3.10\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'D:\\py3.10\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'D:\\py3.10\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'D:\\py3.10\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'D:\\py3.10\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'D:\\py3.10\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'D:\\py3.10\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'D:\\py3.10\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'D:\\py3.10\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'D:\\py3.10\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'D:\\py3.10\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'D:\\py3.10\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'D:\\py3.10\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'D:\\py3.10\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'D:\\py3.10\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.shell',
   'D:\\py3.10\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'D:\\py3.10\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.universal',
   'D:\\py3.10\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'D:\\py3.10\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'D:\\py3.10\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32traceutil',
   'D:\\py3.10\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'D:\\py3.10\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'D:\\py3.10\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree', 'D:\\py3.10\\lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\py3.10\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\py3.10\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\py3.10\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\py3.10\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers', 'D:\\py3.10\\lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', 'D:\\py3.10\\lib\\xml\\parsers\\expat.py', 'PYMODULE'),
  ('xml.sax', 'D:\\py3.10\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\py3.10\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\py3.10\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler', 'D:\\py3.10\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax.saxutils', 'D:\\py3.10\\lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('xml.sax.xmlreader', 'D:\\py3.10\\lib\\xml\\sax\\xmlreader.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\py3.10\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\py3.10\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'D:\\py3.10\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\py3.10\\lib\\zipimport.py', 'PYMODULE')])
